# Optimized Docker ignore for Spring Boot - Alpine Marketing
# Reduces build context size and improves build performance

# === VCS & Development ===
.git/
.gitignore
.gitattributes
.github/
.gitlab-ci.yml

# === Documentation ===
*.md
!README.md
docs/
*.txt
*.pdf
*.docx

# === IDE & Editor Files ===
.idea/
.vscode/
*.iml
*.iws
*.ipr
.settings/
.project
.classpath
.metadata/
.eclipse/
.netbeans/
*.swp
*.swo
*~
.vim/

# === Build Outputs (rebuilt in container) ===
build/
target/
out/
bin/
.gradle/
gradle-app.setting

# === Dependencies (downloaded during build) ===
node_modules/
.pnpm-store/
.npm/
.yarn/

# === Environment & Config ===
.env
.env.*
!.env.template
*.local
.envrc

# === Docker Related (not needed inside container) ===
docker-compose*.yml
Dockerfile*
.dockerignore

# === Deployment & Infrastructure ===
deploy.sh
deploy-*.sh
k8s/
helm/
terraform/
.terraform/
.ansible/

# === Logs & Runtime ===
*.log
logs/
*.pid
*.seed
*.pid.lock

# === Temporary Files ===
*.tmp
*.temp
*.cache
.cache/
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# === Test & Coverage ===
.coverage
coverage/
test-results/
junit.xml
.nyc_output/
htmlcov/
.pytest_cache/
.tox/

# === Backup Files ===
*.bak
*.backup
backup_*
*~
*.orig

# === Spring Boot Specific ===
application-local.properties
application-local.yml
application-dev.properties
application-dev.yml
!application-prod.yml

# === Selenium & Browser ===
chromedriver/
*.driver
.chromedriver/
selenium-debug.log
gecko*.log

# === Security ===
*.pem
*.key
*.crt
*.p12
*.keystore
*.jks
secrets/
.secrets/

# === Large Files ===
*.iso
*.dmg
*.pkg
*.deb
*.rpm

# === Package Managers ===
.m2/
.ivy2/
.sbt/ 