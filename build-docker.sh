#!/bin/bash

# Docker Build Script for Production Deployment
# Ensures the latest code changes are included in the Docker image

set -e  # Exit on any error

echo "🏗️ Building Spring Boot application with latest fixes..."

# Clean and build the application
echo "📦 Running Gradle build..."
./gradlew clean build -x test

# Check if build was successful
if [ ! -f "build/libs/marketing-0.0.1-SNAPSHOT-boot.jar" ]; then
    echo "❌ Build failed - JAR file not found!"
    exit 1
fi

# Copy the latest JAR to the expected location for Docker
echo "📋 Copying latest JAR to app.jar..."
cp build/libs/marketing-0.0.1-SNAPSHOT-boot.jar app.jar

# Verify the copy was successful
if [ ! -f "app.jar" ]; then
    echo "❌ Failed to copy JAR file!"
    exit 1
fi

echo "✅ JAR file ready: app.jar ($(du -h app.jar | cut -f1))"

# Build Docker image
echo "🐳 Building Docker image..."
docker build -t alpine-marketing .

# Verify Docker image was built
if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully: alpine-marketing"
    echo ""
    echo "🚀 Ready for production deployment!"
    echo ""
    echo "📋 Production deployment command:"
    echo "docker run --env-file=\"/root/.env\" -d --name alpine-linkedin \\"
    echo "  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \\"
    echo "  -p 443:443 alpine-marketing \\"
    echo "  --spring.profiles.active=search-tasks,docker \\"
    echo "  --command=execute --tag=green"
    echo ""
    echo "📋 Minimal deployment (if dependencies unavailable):"
    echo "docker run --env-file=\"/root/.env\" -d --name alpine-linkedin \\"
    echo "  -e SELENIUM_ENABLED=false -e SEARCHTASK_ENABLED=false \\"
    echo "  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \\"
    echo "  -p 443:443 alpine-marketing \\"
    echo "  --spring.profiles.active=docker --command=status"
else
    echo "❌ Docker build failed!"
    exit 1
fi
