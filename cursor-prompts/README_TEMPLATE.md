# New Task Template

Task: [Briefly describe the task, e.g.: "Implement a paginated endpoint to get active users with their profiles."]
What I'll do:
- [List clear, small steps/subtasks to accomplish the main task]

**Rules:**
Strictly follow ALL rules, patterns, and constraints defined in:
- @java-spring-boot-core
- @repository-patterns
- @sql-optimization
- @mcp-integration
- @model-switch-strategy

**Additional notes:**
- Use clear, explicit English instructions
- For ambiguous requirements, clarify or propose assumptions
- Always apply proper error handling and code optimization
- Ask for performance hints or run EXPLAIN PLAN for non-trivial queries
- Use markdown checklists for complex tasks

**Workspace context:**
- Tools: Cursor IDE (Claude 4/Sonnet and Gemini 2.5), Github Copilot (IntelliJ Community)
- Aim to minimize token/context consumption (Cursor Education Plan + Github  Education Plan)
