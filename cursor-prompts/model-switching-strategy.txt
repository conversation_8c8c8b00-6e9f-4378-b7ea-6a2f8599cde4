---
alwaysApply: false
description: Model switching strategy and prompting approach
---
# Model Switching Strategy

## Primary Model: Claude-4-Sonnet-Thinking
- Use for complex architectural decisions
- Best for code refactoring and optimization
- Excellent for SQL query optimization
- Strong at identifying N+1 problems
- Include comprehensive error handling patterns
- Show alternative implementation approaches when relevant
- Use <thinking> tags for complex logic reasoning

## Secondary Model: Gemini-2.5-pro-thinking  
- Use when Claude quota is exhausted
- Good for code generation and debugging
- Effective for documentation and comments
- Reliable for standard CRUD operations
- Focus on practical, working solutions
- Emphasize code efficiency and performance
- Provide clear, direct implementations
- Include relevant Spring Boot best practices
- Use structured thinking for problem-solving

## Prompting Strategy for Both Models
Always include these contexts in your prompts:
- Reference active project rules with @java-spring-boot-core
- Mention specific files being worked on
- Specify the exact type of operation (CREATE, READ, UPDATE, DELETE)
- Include performance requirements and constraints