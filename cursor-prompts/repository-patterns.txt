---
type: auto-attached
globs: ["**/*Repository.java", "**/*Service.java"]
description: Repository and Service layer patterns
---

# Repository and Service Layer Standards

## Repository Layer Rules
- Each repository handles ONE entity type
- Use RowMapper for object mapping
- Implement proper exception handling with @Repository annotation
- Use meaningful method names: findByEmailAndActiveStatus(), not findUser()

## Service Layer Rules
- Keep business logic in service layer
- Use @Transactional for operations that modify data
- Implement proper validation before database operations
- Return meaningful DTOs, not raw entities when needed

## Example Repository Structure
```java
@Repository
public class UserRepository {
    private final JdbcTemplate jdbcTemplate;
    private final RowMapper<User> userRowMapper;
    
    // Constructor injection
    public UserRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.userRowMapper = createUserRowMapper();
    }
    
    // CORRECT: Single query with <PERSON><PERSON><PERSON>
    public List<UserWithProfile> findUsersWithProfiles() {
        String sql = """
            SELECT u.id, u.name, u.email, 
                   p.bio, p.avatar_url, p.created_at
            FROM users u
            LEFT JOIN profiles p ON u.id = p.user_id
            WHERE u.active = true
            ORDER BY u.name
        """;
        return jdbcTemplate.query(sql, userWithProfileRowMapper);
    }
}

### 3. **Model Configuration Strategy**

For your two-model approach (Claude-4-Sonnet-Thinking → Gemini-2.5-pro-thinking):

