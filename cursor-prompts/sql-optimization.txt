---
type: auto-attached
globs: ["**/*.sql", "**/*Repository.java", "**/*Dto.java"]
description: SQL optimization rules for database operations
---

# SQL Optimization Rules

## Query Performance Requirements
- Always use EXPLAIN PLAN to verify query performance
- Implement proper JOIN strategies (INNER, LEFT, RIGHT as needed)
- Use subqueries only when JOINs are not feasible
- Implement proper WHERE clause ordering (most selective first)
- Use UNION ALL instead of UNION when duplicates are acceptable

## Batch Operations Pattern
```java
// CORRECT: Batch insert example
public void batchInsertUsers(List<User> users) {
    String sql = "INSERT INTO users (name, email, created_at) VALUES (?, ?, ?)";
    jdbcTemplate.batchUpdate(sql, users, users.size(), 
        (ps, user) -> {
            ps.setString(1, user.getName());
            ps.setString(2, user.getEmail());
            ps.setTimestamp(3, Timestamp.valueOf(user.getCreatedAt()));
        });
}
```

## Pagination Implementation
```java
// CORRECT: Database-level pagination
public Page<User> findUsers(int page, int size) {
    String countSql = "SELECT COUNT(*) FROM users WHERE active = true";
    String dataSql = "SELECT * FROM users WHERE active = true LIMIT ? OFFSET ?";
    
    int total = jdbcTemplate.queryForObject(countSql, Integer.class);
    List<User> users = jdbcTemplate.query(dataSql, userRowMapper, size, page * size);
    
    return new Page<>(users, page, size, total);
}
```
