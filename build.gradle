plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.0'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.alpine'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenCentral()
	gradlePluginPortal()
	maven { url = 'https://repo.spring.io/milestone' } // For Spring Boot 3.5.0 milestone
	maven { url = 'https://repo.spring.io/snapshot' }   // For Spring Boot 3.5.0 snapshot
}

dependencies {
	// Spring Boot Core
	implementation 'org.springframework.boot:spring-boot-starter'
	implementation 'org.springframework.boot:spring-boot-starter-jdbc'

	// Database
	implementation 'com.mysql:mysql-connector-j:8.3.0'

	// Google API
	implementation 'com.google.apis:google-api-services-customsearch:v1-rev20240821-2.0.0'
	implementation 'com.google.api-client:google-api-client:2.2.0'
	implementation 'com.google.http-client:google-http-client-jackson2:1.44.1'

	// Jakarta Annotations (for PostConstruct)
	implementation 'jakarta.annotation:jakarta.annotation-api:2.1.1'

	// JSON Processing
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'

	// HTTP Client
	implementation 'org.apache.httpcomponents.client5:httpclient5:5.3'

	// Utilities
	implementation 'org.apache.commons:commons-lang3:3.14.0'
	implementation 'commons-io:commons-io:2.15.1'

	// Selenium for web scraping (supports Chrome 124)
	implementation 'org.seleniumhq.selenium:selenium-java:4.21.0'

	// Document processing for email extraction
	implementation 'org.apache.pdfbox:pdfbox:2.0.29'                   // PDF processing
	implementation 'org.apache.poi:poi:5.2.4'                         // Word documents (DOC)
	implementation 'org.apache.poi:poi-ooxml:5.2.4'                   // Word documents (DOCX)
	implementation 'org.apache.poi:poi-scratchpad:5.2.4'              // Legacy Word formats (DOC)
	implementation 'org.jsoup:jsoup:1.17.2'                           // Enhanced HTML parsing

	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.junit.jupiter:junit-jupiter:5.10.2'
	testImplementation 'org.mockito:mockito-core:5.10.0'
	testImplementation 'com.h2database:h2:2.2.224' // In-memory DB for testing
}

tasks.withType(Test) {
	useJUnitPlatform()
}

// Enhanced clean task for package restructuring
clean {
	doFirst {
		logger.lifecycle("🧹 Performing enhanced clean for package restructuring...")
	}
	doLast {
		logger.lifecycle("✅ Clean completed - all build artifacts removed")
	}
}

// Ensure clean builds after package restructuring
task cleanBuild(type: Delete) {
	group = 'build'
	description = 'Performs a thorough clean and rebuild (recommended after package restructuring)'
	delete buildDir
	doLast {
		logger.lifecycle("🔄 Clean build completed - safe to rebuild after package changes")
	}
}

// Rebuild task that ensures clean state
task rebuild {
	group = 'build'
	description = 'Clean rebuild with enhanced verification'
	dependsOn cleanBuild, build
	doLast {
		logger.lifecycle("✅ Rebuild completed successfully")
	}
}

// Fixed Spring Boot JAR configuration - no conflicts with Shadow plugin
bootJar {
	archiveBaseName = 'marketing'
	archiveVersion = project.version
	archiveClassifier = 'boot'
	// Spring Boot automatically handles manifest - removed manual configuration to prevent conflicts
}

// Disable plain JAR creation - only create Spring Boot executable JAR
jar {
	enabled = false
}

// Custom tasks for running Spring Boot application
task runGoogleSearchWebpage(type: JavaExec) {
	group = 'application'
	description = 'Run Google Search Webpage URL collection'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=search-webpage']

	// JVM settings for Spring Boot with Google Search profile
	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=googlesearch'
	]

	workingDir = projectDir
}

task runGoogleSearchDocument(type: JavaExec) {
	group = 'application'
	description = 'Run Google Search Document URL collection'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=search-document']

	// JVM settings for Spring Boot with Google Search profile
	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=googlesearch'
	]

	workingDir = projectDir
}

task runStatus(type: JavaExec) {
	group = 'application'
	description = 'Show search status and progress'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=status']

	// JVM settings with Google Search profile
	jvmArgs = [
			'-Dspring.profiles.active=googlesearch'
	]

	workingDir = projectDir
}

task runScrapeEmails(type: JavaExec) {
	group = 'application'
	description = 'Run email scraping from collected URLs'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=scrape-emails']

	// JVM settings for Selenium with Selenium Scraper profile
	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=seleniumscraper'
	]

	workingDir = projectDir
}

task runScraperStatus(type: JavaExec) {
	group = 'application'
	description = 'Show selenium scraper status and statistics'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=scraper-status']

	// JVM settings with Selenium Scraper profile
	jvmArgs = [
			'-Dspring.profiles.active=seleniumscraper'
	]

	workingDir = projectDir
}

// Additional profile-specific tasks
task runGoogleSearchOnly(type: JavaExec) {
	group = 'application'
	description = 'Run Google Search service only (profile: googlesearch)'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=help']

	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=googlesearch'
	]

	workingDir = projectDir
}

task runSeleniumScraperOnly(type: JavaExec) {
	group = 'application'
	description = 'Run Selenium Scraper service only (profile: seleniumscraper)'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=help']

	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=seleniumscraper'
	]

	workingDir = projectDir
}

task runDevelopment(type: JavaExec) {
	group = 'application'
	description = 'Run both services in development mode (profile: dev)'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=help']

	jvmArgs = [
			'-server',
			'-Xmx2g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=dev'
	]

	workingDir = projectDir
}

// Two-Phase Email Discovery System Tasks

task runPhase1Discovery(type: JavaExec) {
	group = 'application'
	description = 'Phase 1: Active API Discovery - Find seed emails using Hunter.io APIs'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=find-seed-emails']

	// JVM settings for Phase 1 with focused Hunter.io usage
	jvmArgs = [
			'-server',
			'-Xmx512m',
			'-Xms256m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=phase1-discovery'
	]

	workingDir = projectDir
}

task runPhase1Status(type: JavaExec) {
	group = 'application'
	description = 'Phase 1: Show API discovery status and seed email statistics'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=status']

	// JVM settings for Phase 1 status
	jvmArgs = [
			'-Dspring.profiles.active=phase1-discovery'
	]

	workingDir = projectDir
}

task runPhase2Inference(type: JavaExec) {
	group = 'application'
	description = 'Phase 2: Passive Pattern Inference - Infer and apply email patterns'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=full-inference']

	// JVM settings for Phase 2 pattern processing
	jvmArgs = [
			'-server',
			'-Xmx1g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=phase2-inference'
	]

	workingDir = projectDir
}

task runPhase2InferOnly(type: JavaExec) {
	group = 'application'
	description = 'Phase 2: Infer patterns only (analyze seed emails)'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=infer-patterns']

	// JVM settings for Phase 2 inference only
	jvmArgs = [
			'-server',
			'-Xmx512m',
			'-Xms256m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=phase2-inference'
	]

	workingDir = projectDir
}

task runPhase2ApplyOnly(type: JavaExec) {
	group = 'application'
	description = 'Phase 2: Apply patterns only (generate emails from patterns)'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=apply-patterns']

	// JVM settings for Phase 2 application only
	jvmArgs = [
			'-server',
			'-Xmx1g',
			'-Xms512m',
			'-XX:+UseG1GC',
			'-Djava.awt.headless=true',
			'-Dspring.profiles.active=phase2-inference'
	]

	workingDir = projectDir
}

task runPhase2Status(type: JavaExec) {
	group = 'application'
	description = 'Phase 2: Show pattern inference status and statistics'

	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'com.alpine.marketing.App'
	args = ['--command=status']

	// JVM settings for Phase 2 status
	jvmArgs = [
			'-Dspring.profiles.active=phase2-inference'
	]

	workingDir = projectDir
}