# School Data Search & Scraping System

https://customsearch.googleapis.com/customsearch/v1?cx=dfsdfds&key=dfasas&q=site:linkedin.com/in%20%22Regent%20High%20School%22&start=91&num=10

**num**
The maximum number of results to return in this single response. Valid values are 1–10 (default is 10). In your URL, num=10 requests ten results per page.
Expertrec custom search engine

**start**
The 1-based index of the first result to return (used for pagination). For example, start=85 asks for results numbered 85–94. 

****The API only allows start up to 91 (so that start + num – 1 ≤ 100).****
# THE API ONLY! allows 100 results per a search query 

## How to Run
1. **Configure API Keys:**
   - Add your Google API keys and settings in `application.yml`.
2. **Create Search Tasks:**
   - `java -jar app.jar --spring.profiles.active=search-tasks --command=execute --tag=blue --priority=1`
3. **Run Searches:**
   - Webpages: `java -jar app.jar --spring.profiles.active=search-tasks --command=execute-task-search-webpage`
   - Documents: `java -jar app.jar --spring.profiles.active=search-tasks --command=execute-task-search-document`
   - LinkedIn: `java -jar app.jar --spring.profiles.active=search-tasks --command=execute-task-search-linkedin-profile`
4. **Scrape Emails:**
   - `java -jar app.jar --spring.profiles.active=seleniumscraper --command=scrape-emails`
5. **Check Progress:**
   - `java -jar app.jar --spring.profiles.active=search-tasks --command=status`

---
- For more details, see inline help (`--command=help`) or the codebase.
- All modules are decoupled and can be run independently as needed.