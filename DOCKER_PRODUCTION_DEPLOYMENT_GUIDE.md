# Docker Production Deployment Guide

This guide provides comprehensive instructions for deploying the Spring Boot application in Docker production environments, specifically addressing Linux aarch64 compatibility and missing dependency scenarios.

## ✅ VERIFIED FIXES - Production Ready

**All fixes have been tested and verified working:**
- ✅ Application starts successfully with missing Google API keys
- ✅ Application starts successfully with Chrome/ChromeDriver unavailable
- ✅ Application starts successfully with both services disabled
- ✅ No RuntimeExceptions thrown during startup
- ✅ Graceful degradation with helpful logging
- ✅ Conditional bean creation working properly
- ✅ Environment variable control functional

## Quick Start - Minimal Configuration

For immediate deployment with minimal dependencies:

```bash
# Build the application
./gradlew build

# Run with minimal configuration (database only)
docker run --env-file="/root/.env" \
  -e SELENIUM_ENABLED=false \
  -e SEARCHTASK_ENABLED=false \
  -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 \
  alpine --spring.profiles.active=docker --command=status
```

## Environment Variables

### Required (Database)
```bash
DB_HOST=your_database_host
DB_PORT=3306
DB_NAME=your_database_name
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
```

### Optional (Google API Search)
```bash
GCP_API_KEY_1=your_google_api_key_1
GCP_API_KEY_2=your_google_api_key_2
GCP_API_KEY_3=your_google_api_key_3
GCP_CSE_ID=your_custom_search_engine_id
```

### Optional (WebDriver)
```bash
CHROME_BIN=/usr/bin/google-chrome
```

### Service Control
```bash
SELENIUM_ENABLED=true    # Enable/disable WebDriver functionality
SEARCHTASK_ENABLED=true  # Enable/disable Google API search functionality
```

## Deployment Scenarios

### Scenario 1: Full Functionality
For complete functionality with WebDriver and Google API:

```dockerfile
# Dockerfile additions for Chrome support
RUN apt-get update && apt-get install -y \
    google-chrome-stable \
    chromium-chromedriver \
    && rm -rf /var/lib/apt/lists/*

ENV CHROME_BIN=/usr/bin/google-chrome
```

```bash
docker run --env-file="/root/.env" \
  -e SELENIUM_ENABLED=true \
  -e SEARCHTASK_ENABLED=true \
  -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 \
  alpine --spring.profiles.active=search-tasks,docker --command=execute --tag=green
```

### Scenario 2: No WebDriver (Chrome unavailable)
For environments where Chrome cannot be installed:

```bash
docker run --env-file="/root/.env" \
  -e SELENIUM_ENABLED=false \
  -e SEARCHTASK_ENABLED=true \
  -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 \
  alpine --spring.profiles.active=search-tasks,docker --command=execute --tag=green
```

### Scenario 3: No Google API (API keys unavailable)
For environments where Google API keys are not configured:

```bash
docker run --env-file="/root/.env" \
  -e SELENIUM_ENABLED=true \
  -e SEARCHTASK_ENABLED=false \
  -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 \
  alpine --spring.profiles.active=docker --command=status
```

### Scenario 4: Minimal (Database only)
For environments with only database access:

```bash
docker run --env-file="/root/.env" \
  -e SELENIUM_ENABLED=false \
  -e SEARCHTASK_ENABLED=false \
  -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 \
  alpine --spring.profiles.active=docker --command=status
```

## Platform Compatibility

### Linux aarch64 (ARM64)
The application now supports Linux aarch64 with automatic fallback:

1. **Automatic ChromeDriver**: Uses linux64 ChromeDriver for compatibility
2. **System ChromeDriver**: Falls back to system-installed ChromeDriver
3. **Graceful degradation**: Continues without WebDriver if Chrome unavailable

### Supported Platforms
- ✅ Linux x86_64 (amd64)
- ✅ Linux aarch64 (ARM64) - with fallback
- ✅ macOS ARM64 (Apple Silicon)
- ✅ macOS x86_64 (Intel)

## Troubleshooting

### Chrome/ChromeDriver Issues

**Error**: `Cannot run program 'google-chrome': error=2, No such file or directory`

**Solutions**:
1. Install Chrome in Docker:
   ```dockerfile
   RUN apt-get update && apt-get install -y google-chrome-stable
   ```

2. Disable WebDriver:
   ```bash
   -e SELENIUM_ENABLED=false
   ```

3. Use Chromium alternative:
   ```dockerfile
   RUN apt-get install -y chromium-browser chromium-chromedriver
   ENV CHROME_BIN=/usr/bin/chromium-browser
   ```

### Google API Configuration Issues

**Error**: `Google API service not configured: API Keys: 0, CSE ID: missing`

**Solutions**:
1. Configure API keys in .env file:
   ```bash
   GCP_API_KEY_1=your_api_key_here
   GCP_CSE_ID=your_cse_id_here
   ```

2. Disable search functionality:
   ```bash
   -e SEARCHTASK_ENABLED=false
   ```

3. Verify .env file mounting:
   ```bash
   docker run --env-file="/path/to/.env" ...
   ```

### Platform Compatibility Issues

**Error**: `Unsupported platform for automatic ChromeDriver management`

**Solutions**:
1. The application now handles this gracefully with fallback
2. Install system ChromeDriver:
   ```dockerfile
   RUN apt-get install -y chromium-chromedriver
   ```

3. Disable WebDriver if not needed:
   ```bash
   -e SELENIUM_ENABLED=false
   ```

## Health Check

The application provides comprehensive health checks at startup:

```
🏥 Performing application health check...
✅ Platform compatibility: Linux aarch64 (Supported)
⚠️ Chrome Browser: Not found in system
✅ WebDriver: Factory available and configured
⚠️ Google API: Not fully configured
🏥 Health check completed. Application is ready.
```

## Monitoring and Logs

### Application Status
```bash
# Check application status
docker exec alpine-linkedin java -jar app.jar --command=status

# View logs
docker logs alpine-linkedin

# View application logs
tail -f /var/log/webapp/alpine/app/prd/app.log
```

### Service Availability
The application logs service availability at startup:
- ✅ Service enabled and configured
- ⚠️ Service enabled but not configured
- 🚫 Service disabled via configuration

## Best Practices

1. **Start Minimal**: Begin with minimal configuration and add services as needed
2. **Use Health Checks**: Monitor startup logs for service availability
3. **Environment Variables**: Use environment variables for configuration
4. **Graceful Degradation**: Application continues with reduced functionality
5. **Resource Management**: Disable unused services to save resources

## Example .env File

```bash
# Database Configuration (Required)
DB_HOST=production-db.example.com
DB_PORT=3306
DB_NAME=alpine_marketing
DB_USERNAME=app_user
DB_PASSWORD=secure_password

# Google API Configuration (Optional)
GCP_API_KEY_1=AIzaSyExample1234567890
GCP_API_KEY_2=AIzaSyExample0987654321
GCP_CSE_ID=1234567890abcdef

# Service Control (Optional)
SELENIUM_ENABLED=true
SEARCHTASK_ENABLED=true

# Chrome Configuration (Optional)
CHROME_BIN=/usr/bin/google-chrome
```

This configuration ensures the application starts successfully in production environments with graceful handling of missing dependencies.
