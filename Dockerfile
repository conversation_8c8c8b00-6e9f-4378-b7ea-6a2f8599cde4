FROM amazoncorretto:17-alpine3.19-jdk

# Install Chrome/Chromium and ChromeDriver (works on both ARM64 and AMD64)
RUN apk add --no-cache \
    chromium \
    chromium-chromedriver \
    dumb-init

# Set environment variables for Docker detection and Chrome paths
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROMEDRIVER_PATH=/usr/bin/chromedriver

# Create log directory
RUN mkdir -p /var/log/webapp/alpine/app/prd/

# Copy application
COPY app.jar /app.jar

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--", "java", "-jar", "/app.jar"]