FROM amazoncorretto:17-alpine3.19-jdk
# Install Chrome & ChromeDriver dependencies for ARM64/AMD64 compatibility
RUN apk add --no-cache \
    chromium \
    chromium-chromedriver \
    harfbuzz \
    nss \
    freetype \
    ttf-freefont \
    font-noto-emoji \
    bash \
    dumb-init

# Set environment variables for Chrome/Chromium
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_PATH=/usr/lib/chromium/
ENV CHROMEDRIVER_PATH=/usr/bin/chromedriver
# Chrome/Chromium configuration for headless operation
ENV CHROME_OPTS="--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-extensions --headless=new"

RUN mkdir -p /var/log/webapp/alpine/app/prd/

COPY app.jar /app.jar
# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--", "java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app.jar"]