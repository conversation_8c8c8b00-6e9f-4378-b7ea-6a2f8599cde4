16:43:51.515 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 37865 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:43:51.516 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:43:51.584 [main] WARN  o.s.c.a.AnnotationConfigApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.alpine.marketing.App]
16:43:51.602 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.alpine.marketing.App]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:194)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:31)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'googleApiService' for bean class [com.alpine.marketing.searchtask.service.GoogleApiService] conflicts with existing, non-compatible bean definition of same name and class [com.alpine.marketing.googlesearch.service.GoogleApiService]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:346)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:281)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:204)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:172)
	... 12 common frames omitted
16:43:51.603 [main] ERROR com.alpine.marketing.App - ❌ Application failed: Failed to parse configuration class [com.alpine.marketing.App]
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.alpine.marketing.App]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:194)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:31)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'googleApiService' for bean class [com.alpine.marketing.searchtask.service.GoogleApiService] conflicts with existing, non-compatible bean definition of same name and class [com.alpine.marketing.googlesearch.service.GoogleApiService]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:346)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:281)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:204)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:172)
	... 12 common frames omitted
16:46:26.706 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 38675 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:46:26.708 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:46:27.081 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:46:27.088 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:46:27.144 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:46:27.144 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:46:27.145 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:46:27.145 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:46:27.145 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:46:27.145 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:46:27.145 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:46:27.260 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:46:27.260 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:46:27.261 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:46:27.261 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:46:27.261 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:46:27.261 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:46:27.285 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:46:27.285 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:46:27.285 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:46:27.285 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:46:28.341 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:46:28.389 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:46:28.389 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:46:28.391 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:46:28.391 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:46:28.391 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:46:28.399 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:46:28.399 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:46:28.399 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:46:30.429 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:46:30.430 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:46:30.446 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:46:30.446 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:46:30.449 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 2980ms (range: 1500-4000ms, momentum: 1.00)
16:46:33.434 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:46:33.435 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:46:33.436 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:46:33.436 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:46:33.588 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:46:33.773 [main] INFO  com.alpine.marketing.App - Started App in 7.276 seconds (process running for 8.861)
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create,--tag=blue' with tag: 'null', batchSize: 50
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner Commands (Profile: search-task):
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Commands:
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=create --tag=<tag>
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Create search tasks from templates for the specified tag
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=create --tag=blue
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.778 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=execute --tag=<tag> [--batchSize=<size>]
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Execute pending search tasks for the specified tag
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=execute --tag=blue --batchSize=50
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=retry
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Reset all quota-failed tasks back to PENDING status
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=retry
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=status
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Show task statistics and current status
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=status
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=help
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Show this help message
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Usage Examples:
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=create --tag=blue
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=execute --tag=blue --batchSize=25
16:46:33.779 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=status
16:46:33.785 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:46:33.786 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:46:33.786 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:46:33.864 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
16:46:33.864 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:48:38.599 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 39265 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:48:38.601 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:48:38.977 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:48:38.986 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:48:39.027 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:48:39.027 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:48:39.027 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:48:39.028 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:48:39.028 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:48:39.028 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:48:39.028 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:48:39.125 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:48:39.125 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:48:39.126 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:48:39.126 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:48:39.126 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:48:39.126 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:48:39.143 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:48:39.143 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:48:39.144 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:48:39.144 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:48:40.033 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:48:40.083 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:48:40.083 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:48:40.086 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:48:40.086 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:48:40.086 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:48:40.093 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:48:40.093 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:48:40.093 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:48:40.093 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:48:40.093 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:48:40.094 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:48:40.094 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:48:40.094 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:48:40.094 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:48:41.518 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:48:41.519 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:48:41.532 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:48:41.532 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:48:41.539 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3073ms (range: 1500-4000ms, momentum: 1.00)
16:48:44.617 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:48:44.624 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:48:44.625 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:48:44.625 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:48:44.742 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:48:44.917 [main] INFO  com.alpine.marketing.App - Started App in 6.523 seconds (process running for 7.138)
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create,tag=blue' with tag: 'null', batchSize: 50
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner Commands (Profile: search-task):
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Commands:
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=create --tag=<tag>
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Create search tasks from templates for the specified tag
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=create --tag=blue
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=execute --tag=<tag> [--batchSize=<size>]
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Execute pending search tasks for the specified tag
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=execute --tag=blue --batchSize=50
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=retry
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Reset all quota-failed tasks back to PENDING status
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=retry
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=status
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Show task statistics and current status
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Example: --command=status
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   --command=help
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -     Show this help message
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Usage Examples:
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=create --tag=blue
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=execute --tag=blue --batchSize=25
16:48:44.923 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   java -jar app.jar --spring.profiles.active=search-task --command=status
16:48:44.929 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:48:44.929 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:48:44.929 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:48:45.014 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
16:48:45.015 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:49:09.430 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 39424 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:49:09.431 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:49:09.808 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:49:09.816 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:49:09.854 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:49:09.855 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:49:09.855 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:49:09.855 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:49:09.856 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:49:09.856 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:49:09.856 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:49:09.958 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:49:09.958 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:49:09.959 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:49:09.959 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:49:09.959 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:49:09.959 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:49:09.981 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:49:09.981 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:49:09.981 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:49:09.981 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:49:10.919 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:49:10.966 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:49:10.967 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:49:10.968 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:49:10.968 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:49:10.968 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:49:10.974 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:49:10.974 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:49:10.974 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:49:10.974 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:49:10.974 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:49:10.975 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:49:10.975 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:49:10.975 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:49:10.975 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:49:12.516 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:49:12.517 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:49:12.531 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:49:12.531 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:49:12.534 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3352ms (range: 1500-4000ms, momentum: 1.00)
16:49:15.892 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:49:15.895 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:49:15.895 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:49:15.895 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:49:16.005 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:49:16.176 [main] INFO  com.alpine.marketing.App - Started App in 6.955 seconds (process running for 7.581)
16:49:16.182 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:49:16.182 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 50
16:51:03.215 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
16:51:03.217 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
16:51:03.258 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:51:03.302 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:51:03.303 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:51:03.303 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:51:03.355 [SpringApplicationShutdownHook] WARN  c.a.m.s.config.SeleniumConfig - ⚠️ Error during WebDriver shutdown: Timed out waiting for driver server to stop.
Build info: version: '4.31.0', revision: '1ef9f18787*'
System info: os.name: 'Mac OS X', os.arch: 'aarch64', os.version: '15.6', java.version: '17.0.15'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [2a443b3babef11a367e56134155d70dc, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.101, chrome: {chromedriverVersion: 138.0.7152.0 (65d57856e09ea..., userDataDir: /tmp/chrome-user-data}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49228}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: mac, proxy: Proxy(), se:cdp: ws://localhost:49228/devtoo..., se:cdpVersion: 138.0.7204.101, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 2a443b3babef11a367e56134155d70dc
16:51:03.355 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:51:13.408 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 40008 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:51:13.409 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:51:13.790 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:51:13.799 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:51:13.836 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:51:13.837 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:13.837 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:13.838 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:51:13.838 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:51:13.838 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:13.838 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:13.962 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:13.962 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:13.963 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:51:13.963 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:13.963 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:13.963 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:51:13.987 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:51:13.987 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:51:13.987 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:51:13.988 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:14.898 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:14.949 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:51:14.949 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:51:14.951 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:14.951 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:51:14.951 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:14.958 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:14.958 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:51:14.958 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:51:16.346 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:51:16.350 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:51:16.363 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:51:16.364 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:51:16.370 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3133ms (range: 1500-4000ms, momentum: 1.00)
16:51:19.511 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:51:19.513 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:51:19.513 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:51:19.513 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:51:19.618 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:51:19.789 [main] INFO  com.alpine.marketing.App - Started App in 6.587 seconds (process running for 7.173)
16:51:19.796 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:51:19.796 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'status' with tag: 'blue', batchSize: 1
16:51:19.796 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Task Statistics:
16:51:19.803 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:51:19.998 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@47eaf55c
16:51:20.002 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:51:20.049 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total: 0
16:51:20.049 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 0
16:51:20.049 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 0
16:51:20.049 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
16:51:20.049 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Error: 0
16:51:20.056 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:51:20.056 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:51:20.056 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:51:20.143 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
16:51:20.143 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:51:20.143 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:51:20.149 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:51:28.887 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 40098 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:51:28.888 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:51:29.241 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:51:29.249 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:51:29.283 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:51:29.284 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:29.284 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:29.285 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:51:29.285 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:51:29.285 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:29.285 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:29.386 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:29.386 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:29.387 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:51:29.387 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:29.387 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:29.387 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:51:29.405 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:51:29.405 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:51:29.405 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:51:29.405 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:30.323 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:30.372 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:51:30.372 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:51:30.374 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:30.374 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:51:30.374 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:30.381 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:30.381 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:30.381 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:51:30.381 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:51:30.381 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:51:30.381 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:51:30.381 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:51:30.382 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:51:30.382 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:51:31.648 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:51:31.649 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:51:31.667 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:51:31.667 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:51:31.670 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3335ms (range: 1500-4000ms, momentum: 1.00)
16:51:35.011 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:51:35.012 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:51:35.012 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:51:35.012 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:51:35.117 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:51:35.281 [main] INFO  com.alpine.marketing.App - Started App in 6.593 seconds (process running for 7.197)
16:51:35.286 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:51:35.286 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 1
16:51:42.298 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
16:51:42.300 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
16:51:42.306 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:51:42.360 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:51:42.360 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:51:42.360 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:51:42.397 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@349312d5
16:51:42.398 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:51:42.409 [SpringApplicationShutdownHook] WARN  c.a.m.s.config.SeleniumConfig - ⚠️ Error during WebDriver shutdown: Timed out waiting for driver server to stop.
Build info: version: '4.31.0', revision: '1ef9f18787*'
System info: os.name: 'Mac OS X', os.arch: 'aarch64', os.version: '15.6', java.version: '17.0.15'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [cc3978c822a9136943991a6ed7d9502b, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.101, chrome: {chromedriverVersion: 138.0.7152.0 (65d57856e09ea..., userDataDir: /tmp/chrome-user-data}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49480}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: mac, proxy: Proxy(), se:cdp: ws://localhost:49480/devtoo..., se:cdpVersion: 138.0.7204.101, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: cc3978c822a9136943991a6ed7d9502b
16:51:42.409 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:51:42.409 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:51:42.412 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:51:54.492 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 40228 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:51:54.496 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:51:54.874 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:51:54.882 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:51:54.918 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:51:54.919 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:54.919 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:54.919 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:51:54.919 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:51:54.919 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:54.919 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:55.025 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:55.025 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:55.026 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:51:55.026 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:55.026 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:55.026 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:51:55.041 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:51:55.042 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:51:55.042 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:51:55.042 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:55.957 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:51:56.009 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:51:56.009 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:51:56.011 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:56.011 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:51:56.011 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:51:56.018 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:51:56.018 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:51:56.018 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:51:57.542 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:51:57.542 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:51:57.552 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:51:57.552 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:51:57.555 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3618ms (range: 1500-4000ms, momentum: 1.00)
16:52:01.178 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:52:01.181 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:52:01.182 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:52:01.182 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:52:01.303 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:52:01.466 [main] INFO  com.alpine.marketing.App - Started App in 7.188 seconds (process running for 7.793)
16:52:01.471 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:52:01.471 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 3
16:52:05.427 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
16:52:08.776 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
16:52:10.288 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:52:11.021 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@774f2992
16:52:11.029 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:52:11.141 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 0 schools with valid web domains
16:52:35.257 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 3 search query templates for tag 'blue'
16:52:35.261 [main] WARN  c.a.m.s.service.SearchTaskService - No schools found with web domains - cannot create tasks
16:52:35.261 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully
16:52:35.269 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:52:35.269 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:52:35.269 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:52:35.329 [SpringApplicationShutdownHook] WARN  c.a.m.s.config.SeleniumConfig - ⚠️ Error during WebDriver shutdown: Timed out waiting for driver server to stop.
Build info: version: '4.31.0', revision: '1ef9f18787*'
System info: os.name: 'Mac OS X', os.arch: 'aarch64', os.version: '15.6', java.version: '17.0.15'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [86ba4f4d960c34b2c21ce372d6abb39c, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.101, chrome: {chromedriverVersion: 138.0.7152.0 (65d57856e09ea..., userDataDir: /tmp/chrome-user-data}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49571}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: mac, proxy: Proxy(), se:cdp: ws://localhost:49571/devtoo..., se:cdpVersion: 138.0.7204.101, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 86ba4f4d960c34b2c21ce372d6abb39c
16:52:35.329 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:52:35.329 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:52:35.341 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:52:55.741 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 40539 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:52:55.743 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:52:56.106 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:52:56.114 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:52:56.149 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:52:56.150 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:52:56.150 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:52:56.150 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:52:56.150 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:52:56.150 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:52:56.150 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:52:56.258 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:52:56.258 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:52:56.258 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:52:56.258 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:52:56.258 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:52:56.259 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:52:56.275 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:52:56.275 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:52:56.276 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:52:56.276 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:52:57.373 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:52:57.424 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:52:57.424 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:52:57.425 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:52:57.425 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:52:57.425 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:52:57.430 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:52:57.430 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:52:57.430 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:52:57.431 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:52:58.936 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:52:58.937 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:52:58.951 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:52:58.951 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:52:58.956 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3143ms (range: 1500-4000ms, momentum: 1.00)
16:53:02.105 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:53:02.107 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:53:02.107 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:53:02.107 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:53:02.229 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:53:02.418 [main] INFO  com.alpine.marketing.App - Started App in 6.889 seconds (process running for 7.527)
16:53:02.423 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:53:02.423 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 3
16:53:05.317 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
16:53:06.354 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
16:53:06.916 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:53:07.655 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@58186f9f
16:53:07.663 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:53:08.022 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
16:53:14.966 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 3 search query templates for tag 'blue'
16:53:37.894 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 3 templates for tag 'blue'
16:54:24.131 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 8937 tasks (2979 schools × 3 templates)
16:55:20.519 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 8937 search tasks
16:55:20.520 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 8937 search tasks for tag 'blue'
16:55:20.520 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully
16:55:20.537 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
16:55:20.538 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
16:55:20.538 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
16:55:20.634 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
16:55:20.635 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
16:55:20.635 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:55:20.641 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:59:50.155 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 42273 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
16:59:50.156 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
16:59:50.545 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
16:59:50.554 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
16:59:50.593 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
16:59:50.593 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:59:50.593 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:59:50.594 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
16:59:50.594 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
16:59:50.594 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:59:50.594 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:59:50.700 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:59:50.700 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:59:50.700 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
16:59:50.701 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:59:50.701 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:59:50.701 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
16:59:50.721 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
16:59:50.721 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
16:59:50.721 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
16:59:50.722 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:59:51.605 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
16:59:51.654 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
16:59:51.654 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
16:59:51.656 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:59:51.656 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
16:59:51.656 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
16:59:51.664 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
16:59:51.664 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
16:59:51.664 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
16:59:53.087 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
16:59:53.088 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
16:59:53.106 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
16:59:53.106 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
16:59:53.109 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3104ms (range: 1500-4000ms, momentum: 1.00)
16:59:56.217 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
16:59:56.219 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
16:59:56.220 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
16:59:56.220 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
16:59:56.348 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
16:59:56.514 [main] INFO  com.alpine.marketing.App - Started App in 6.566 seconds (process running for 7.189)
16:59:56.519 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
16:59:56.520 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 3
16:59:59.604 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
17:00:00.541 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
17:00:01.077 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:00:01.833 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e3252
17:00:01.843 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:00:02.226 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
17:00:04.570 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 3 search query templates for tag 'blue'
17:00:04.571 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 3 templates for tag 'blue'
17:00:05.272 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 8937 tasks (2979 schools × 3 templates)
17:01:00.973 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 8937 search tasks
17:01:00.974 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 8937 search tasks for tag 'blue'
17:01:00.975 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully
17:01:00.990 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
17:01:00.990 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
17:01:00.990 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
17:01:01.092 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
17:01:01.092 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
17:01:01.092 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
17:01:01.099 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
17:04:32.948 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 43483 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
17:04:32.950 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
17:04:33.298 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
17:04:33.306 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
17:04:33.328 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
17:04:33.328 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:04:33.328 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:04:33.328 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
17:04:33.328 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
17:04:33.328 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:04:33.328 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:04:33.384 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:04:33.384 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:04:33.384 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
17:04:33.384 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:04:33.384 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:04:33.385 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
17:04:33.403 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
17:04:33.403 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
17:04:33.403 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
17:04:33.404 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:04:34.185 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3170867 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:04:34.220 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
17:04:34.221 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
17:04:34.221 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:04:34.221 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
17:04:34.221 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:04:34.226 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:04:34.226 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
17:04:34.226 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
17:04:35.497 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
17:04:35.497 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
17:04:35.503 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
17:04:35.503 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
17:04:35.504 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3260ms (range: 1500-4000ms, momentum: 1.00)
17:04:38.768 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
17:04:38.770 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
17:04:38.772 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
17:04:38.772 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
17:04:38.828 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
17:04:38.919 [main] INFO  com.alpine.marketing.App - Started App in 6.185 seconds (process running for 6.626)
17:04:38.921 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
17:04:38.921 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'create' with tag: 'blue', batchSize: 3
17:04:38.921 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
17:04:38.921 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
17:04:38.924 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:04:39.010 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@36211bbc
17:04:39.012 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:04:39.045 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
17:04:39.062 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 3 search query templates for tag 'blue'
17:04:39.062 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 3 templates for tag 'blue'
17:04:39.070 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 8937 tasks (2979 schools × 3 templates)
17:04:40.332 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 8937 search tasks
17:04:40.332 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 8937 search tasks for tag 'blue'
17:04:40.332 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully
17:04:40.335 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
17:04:40.335 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
17:04:40.335 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
17:04:40.395 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
17:04:40.395 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
17:04:40.395 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
17:04:40.398 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
17:12:29.999 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 45620 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
17:12:30.000 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
17:12:30.350 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
17:12:30.358 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
17:12:30.376 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
17:12:30.377 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:12:30.377 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:12:30.377 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
17:12:30.377 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
17:12:30.377 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:12:30.377 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:12:30.434 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:12:30.435 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:12:30.435 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
17:12:30.435 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:12:30.435 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:12:30.435 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
17:12:30.450 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
17:12:30.450 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
17:12:30.450 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
17:12:30.450 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:12:31.133 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:12:31.166 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
17:12:31.167 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
17:12:31.167 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:12:31.167 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
17:12:31.167 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:12:31.171 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:12:31.171 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
17:12:31.171 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
17:12:32.461 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
17:12:32.461 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
17:12:32.467 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
17:12:32.467 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
17:12:32.469 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 2238ms (range: 1500-4000ms, momentum: 1.00)
17:12:34.712 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
17:12:34.713 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
17:12:34.713 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
17:12:34.714 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
17:12:34.758 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
17:12:34.843 [main] INFO  com.alpine.marketing.App - Started App in 5.044 seconds (process running for 5.484)
17:12:34.845 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
17:12:34.845 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'status' with tag: 'blue', batchSize: 3
17:12:34.845 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Task Statistics:
17:12:34.848 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:12:34.987 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b3f4bd8
17:12:34.988 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total: 8937
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 8937
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 0
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Error: 0
17:12:35.040 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completion Rate: {:.1f}%
17:12:35.043 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
17:12:35.043 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
17:12:35.043 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
17:12:35.135 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
17:12:35.135 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
17:12:35.136 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
17:12:35.169 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
17:16:52.284 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 46776 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
17:16:52.285 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
17:16:52.682 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
17:16:52.715 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
17:16:52.766 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
17:16:52.767 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:16:52.767 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:16:52.769 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
17:16:52.769 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
17:16:52.769 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:16:52.769 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:16:52.888 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:16:52.888 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:16:52.889 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
17:16:52.889 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:16:52.889 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:16:52.890 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
17:16:52.914 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
17:16:52.914 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
17:16:52.914 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
17:16:52.914 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:16:54.811 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
17:16:54.866 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
17:16:54.866 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
17:16:54.868 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:16:54.868 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
17:16:54.868 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
17:16:54.876 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
17:16:54.876 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
17:16:54.876 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
17:16:54.876 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
17:16:54.877 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
17:16:54.877 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
17:16:54.877 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
17:16:54.877 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
17:16:54.877 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
17:16:56.517 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
17:16:56.518 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
17:16:56.532 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
17:16:56.532 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
17:16:56.536 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 2877ms (range: 1500-4000ms, momentum: 1.00)
17:16:59.418 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
17:16:59.420 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
17:16:59.420 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
17:16:59.420 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
17:16:59.536 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
17:16:59.718 [main] INFO  com.alpine.marketing.App - Started App in 7.636 seconds (process running for 9.071)
17:16:59.723 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 SearchTask Runner started with profile: search-task
17:16:59.723 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: 'execute' with tag: 'blue', batchSize: 3
17:17:07.248 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ⚡ Executing pending tasks for tag: 'blue' with batch size: 3
17:17:16.290 [main] INFO  c.a.m.s.service.SearchTaskService - Executing pending tasks for tag 'blue' with batch size 3
17:17:23.956 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:17:24.795 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34ab26a
17:17:24.805 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:17:25.001 [main] INFO  c.a.m.s.r.SearchTaskRepository - Found 3 pending tasks for tag 'blue' (limit: 3)
17:17:36.312 [main] INFO  c.a.m.s.service.SearchTaskService - Found 3 pending tasks to execute for tag 'blue'
17:19:56.555 [HikariPool-1:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m31s643ms).
17:19:59.560 [main] INFO  c.a.m.s.service.SearchTaskService - Executing task 3036 (school URN: 136667, query: 'site:linkedin.com/in intext:"South Hunsley School and Sixth Form College" (intext:"QTS" OR intext:"PGCE")')
17:20:13.617 [main] INFO  c.a.m.s.service.GoogleApiService - Executing search for task 3036: 'site:linkedin.com/in intext:"South Hunsley School and Sixth Form College" (intext:"QTS" OR intext:"PGCE")'
17:20:38.736 [main] INFO  c.a.m.s.service.GoogleApiService - Search successful for task 3036: 2 URLs found
17:21:18.233 [HikariPool-1:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=48s364ms).
17:21:40.050 [main] INFO  c.a.m.s.r.SearchResultRepository - Successfully saved 2 search results
17:21:51.568 [main] INFO  c.a.m.s.service.SearchTaskService - Saved 2 URLs for task 3036
17:22:13.933 [main] INFO  c.a.m.s.service.SearchTaskService - ✅ Task 3036 completed successfully: 2 URLs found
17:22:17.490 [main] INFO  c.a.m.s.service.SearchTaskService - Executing task 3039 (school URN: 136668, query: 'site:linkedin.com/in intext:"Lipson Co-operative Academy" (intext:"QTS" OR intext:"PGCE")')
17:22:18.691 [main] INFO  c.a.m.s.service.GoogleApiService - Executing search for task 3039: 'site:linkedin.com/in intext:"Lipson Co-operative Academy" (intext:"QTS" OR intext:"PGCE")'
17:22:20.095 [main] INFO  c.a.m.s.service.GoogleApiService - Search successful for task 3039: 10 URLs found
17:22:29.301 [main] INFO  c.a.m.s.r.SearchResultRepository - Successfully saved 10 search results
17:22:29.302 [main] INFO  c.a.m.s.service.SearchTaskService - Saved 10 URLs for task 3039
17:22:35.837 [main] INFO  c.a.m.s.service.SearchTaskService - ✅ Task 3039 completed successfully: 10 URLs found
17:22:35.838 [main] INFO  c.a.m.s.service.SearchTaskService - Executing task 3042 (school URN: 136669, query: 'site:linkedin.com/in intext:"Brampton Manor Academy" (intext:"QTS" OR intext:"PGCE")')
17:22:45.353 [main] INFO  c.a.m.s.service.GoogleApiService - Executing search for task 3042: 'site:linkedin.com/in intext:"Brampton Manor Academy" (intext:"QTS" OR intext:"PGCE")'
17:22:46.138 [main] INFO  c.a.m.s.service.GoogleApiService - Search successful for task 3042: 10 URLs found
17:22:48.489 [main] INFO  c.a.m.s.r.SearchResultRepository - Successfully saved 10 search results
17:22:48.490 [main] INFO  c.a.m.s.service.SearchTaskService - Saved 10 URLs for task 3042
17:22:51.109 [main] INFO  c.a.m.s.service.SearchTaskService - ✅ Task 3042 completed successfully: 10 URLs found
17:22:51.110 [main] INFO  c.a.m.s.service.SearchTaskService - Task execution completed for tag 'blue': 3 successful, 0 quota failed, 0 errors
17:22:51.111 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task execution completed
17:22:51.124 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
17:22:51.124 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
17:22:51.125 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
17:22:51.223 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
17:22:51.224 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
17:22:51.224 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
17:22:51.231 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
18:24:30.251 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 67453 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
18:24:30.252 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
18:24:30.617 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
18:24:30.624 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
18:24:30.647 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
18:24:30.647 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:30.647 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:30.647 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
18:24:30.647 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
18:24:30.647 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:30.647 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:30.710 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:30.710 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:30.711 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
18:24:30.711 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:30.711 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:30.711 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
18:24:30.733 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
18:24:30.733 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
18:24:30.733 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
18:24:30.733 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
18:24:31.466 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
18:24:31.501 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
18:24:31.501 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
18:24:31.501 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
18:24:31.501 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
18:24:31.502 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
18:24:31.505 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:31.505 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
18:24:31.505 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
18:24:33.382 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
18:24:33.383 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
18:24:33.389 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
18:24:33.389 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
18:24:33.390 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3235ms (range: 1500-4000ms, momentum: 1.00)
18:24:36.631 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
18:24:36.632 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
18:24:36.632 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
18:24:36.632 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
18:24:36.680 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
18:24:36.784 [main] INFO  com.alpine.marketing.App - Started App in 6.742 seconds (process running for 7.147)
18:24:36.785 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 Search Task Runner started with profile: search-task
18:24:36.785 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: create with tag: blue
18:24:36.785 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
18:24:36.785 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
18:24:36.788 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
18:24:36.874 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b3f4bd8
18:24:36.875 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
18:24:36.913 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
18:24:36.924 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 3 search query templates for tag 'blue'
18:24:36.924 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 3 templates for tag 'blue'
18:24:36.935 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 8937 tasks (2979 schools × 3 templates)
18:24:38.343 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 8937 search tasks
18:24:38.344 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 8937 search tasks for tag 'blue'
18:24:38.344 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully for tag: 'blue'
18:24:38.344 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Search Task Runner Status
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Task Overview:
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total Tasks: 8937
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 8937
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 0
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Errors: 0
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completion Rate: {:.1f}%
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📝 Recommended Next Action:
18:24:38.349 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Run 'execute' command to process 8937 pending tasks
18:24:38.352 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
18:24:38.352 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
18:24:38.352 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
18:24:38.434 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
18:24:38.434 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
18:24:38.434 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
18:24:38.437 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
18:24:55.607 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 67579 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
18:24:55.608 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
18:24:56.014 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
18:24:56.044 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
18:24:56.095 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
18:24:56.096 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:56.096 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:56.096 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
18:24:56.096 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
18:24:56.096 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:56.096 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:56.210 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:56.210 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:56.211 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
18:24:56.211 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:56.211 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:56.211 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
18:24:56.232 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
18:24:56.232 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
18:24:56.233 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
18:24:56.233 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
18:24:57.240 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
18:24:57.297 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
18:24:57.297 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
18:24:57.299 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
18:24:57.299 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
18:24:57.299 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
18:24:57.307 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
18:24:57.307 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
18:24:57.307 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
18:24:57.308 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
18:24:58.913 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
18:24:58.914 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
18:24:58.936 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
18:24:58.936 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
18:24:58.938 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3069ms (range: 1500-4000ms, momentum: 1.00)
18:25:02.013 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
18:25:02.014 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
18:25:02.014 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
18:25:02.015 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
18:25:02.120 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
18:25:02.298 [main] INFO  com.alpine.marketing.App - Started App in 6.91 seconds (process running for 8.33)
18:25:02.303 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 Search Task Runner started with profile: search-task
18:25:02.303 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: execute with tag: blue
18:25:05.624 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🔍 Executing all pending search tasks for tag: 'blue'
18:25:07.055 [main] INFO  c.a.m.s.service.SearchTaskService - Executing all pending tasks for tag 'blue'
18:25:08.526 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
18:25:09.261 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@316d30ad
18:25:09.270 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
18:25:11.495 [main] INFO  c.a.m.s.r.SearchTaskRepository - Found 8937 pending tasks for tag 'blue'
18:25:16.683 [main] INFO  c.a.m.s.service.SearchTaskService - Found 8937 pending tasks to execute for tag 'blue'
18:25:20.863 [main] INFO  c.a.m.s.service.SearchTaskService - Executing task 3 (school URN: 100049, query: 'site:linkedin.com/in intext:"Haverstock School" (intext:"QTS" OR intext:"PGCE")', target: 100 results)
18:29:39.795 [HikariPool-1:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m30s419ms).
18:29:45.387 [main] INFO  c.a.m.s.service.GoogleApiService - Executing dynamic paginated search for task 3: 'site:linkedin.com/in intext:"Haverstock School" (intext:"QTS" OR intext:"PGCE")' (target: 100 results)
18:30:00.277 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 1 (start index 1) for task 3 - collected 0/100 results so far
18:30:07.080 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...eQe4
18:30:07.082 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 1) with API key: AIza...
18:30:08.689 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 1): 10 URLs found
18:30:14.723 [main] INFO  c.a.m.s.service.GoogleApiService - Page 1 completed: 10 URLs found (total so far: 10/100)
18:30:15.831 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 2 (start index 11) for task 3 - collected 10/100 results so far
18:30:15.832 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...T6lU
18:30:15.832 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 11) with API key: AIza...
18:30:16.647 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 11): 10 URLs found
18:30:16.648 [main] INFO  c.a.m.s.service.GoogleApiService - Page 2 completed: 10 URLs found (total so far: 20/100)
18:30:16.648 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 3 (start index 21) for task 3 - collected 20/100 results so far
18:30:16.648 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...fv5c
18:30:16.649 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 21) with API key: AIza...
18:30:16.649 [main] INFO  c.a.m.s.service.GoogleApiService - Rate limiting: sleeping 184ms
18:30:17.320 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 21): 10 URLs found
18:30:17.321 [main] INFO  c.a.m.s.service.GoogleApiService - Page 3 completed: 10 URLs found (total so far: 30/100)
18:30:17.321 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 4 (start index 31) for task 3 - collected 30/100 results so far
18:30:17.321 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...d6Nk
18:30:17.322 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 31) with API key: AIza...
18:30:17.322 [main] INFO  c.a.m.s.service.GoogleApiService - Rate limiting: sleeping 516ms
18:30:18.433 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 31): 10 URLs found
18:30:18.434 [main] INFO  c.a.m.s.service.GoogleApiService - Page 4 completed: 10 URLs found (total so far: 40/100)
18:30:18.435 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 5 (start index 41) for task 3 - collected 40/100 results so far
18:30:18.435 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...FS8I
18:30:18.435 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 41) with API key: AIza...
18:30:18.435 [main] INFO  c.a.m.s.service.GoogleApiService - Rate limiting: sleeping 408ms
18:30:19.478 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 41): 10 URLs found
18:30:19.479 [main] INFO  c.a.m.s.service.GoogleApiService - Page 5 completed: 10 URLs found (total so far: 50/100)
18:30:19.479 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 6 (start index 51) for task 3 - collected 50/100 results so far
18:30:19.480 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...QaRc
18:30:19.480 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 51) with API key: AIza...
18:30:19.480 [main] INFO  c.a.m.s.service.GoogleApiService - Rate limiting: sleeping 365ms
18:30:20.155 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 51): 10 URLs found
18:30:20.156 [main] INFO  c.a.m.s.service.GoogleApiService - Page 6 completed: 10 URLs found (total so far: 60/100)
18:30:20.156 [main] INFO  c.a.m.s.service.GoogleApiService - Fetching page 7 (start index 61) for task 3 - collected 60/100 results so far
18:30:20.157 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Retrieved API key: AIza...QweA
18:30:20.157 [main] INFO  c.a.m.s.service.GoogleApiService - Attempting page fetch (start: 61) with API key: AIza...
18:30:20.157 [main] INFO  c.a.m.s.service.GoogleApiService - Rate limiting: sleeping 689ms
18:30:22.331 [main] INFO  c.a.m.s.service.GoogleApiService - Page fetch successful (start: 61): 3 URLs found
18:30:22.332 [main] INFO  c.a.m.s.service.GoogleApiService - Page 7 completed: 3 URLs found (total so far: 63/100)
18:30:22.332 [main] INFO  c.a.m.s.service.GoogleApiService - Last page reached for task 3 (page 7 returned 3 results)
18:30:22.332 [main] INFO  c.a.m.s.service.GoogleApiService - Dynamic paginated search completed for task 3: 63 URLs found across 7 API calls (target: 100)
18:30:49.243 [main] INFO  c.a.m.s.r.SearchResultRepository - Successfully saved 63 search results
18:30:49.244 [main] INFO  c.a.m.s.service.SearchTaskService - Saved 63 URLs for task 3
18:30:49.328 [main] ERROR c.a.m.s.r.SearchTaskRepository - Failed to update task 3 completion: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:141)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
18:30:49.356 [main] ERROR c.a.m.s.r.SearchTaskRepository - Failed to update task 3 completion: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:160)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
18:30:49.360 [main] ERROR c.a.m.s.service.SearchTaskService - Failed to execute pending tasks for tag 'blue': Failed to update task completion
java.lang.RuntimeException: Failed to update task completion
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:154)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:160)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	... 34 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
18:30:49.365 [main] ERROR c.a.marketing.cmd.SearchTaskRunner - ❌ Task execution failed for tag 'blue': Failed to execute pending tasks
java.lang.RuntimeException: Failed to execute pending tasks
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:172)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: java.lang.RuntimeException: Failed to update task completion
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:154)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:160)
	... 22 common frames omitted
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	... 34 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
18:30:49.416 [main] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.RuntimeException: Task execution failed
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:99)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: java.lang.RuntimeException: Failed to execute pending tasks
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:172)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	... 21 common frames omitted
Caused by: java.lang.RuntimeException: Failed to update task completion
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:154)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:160)
	... 22 common frames omitted
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	... 34 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
18:30:49.422 [main] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
18:30:49.422 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
18:30:49.422 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
18:30:49.543 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
18:30:49.543 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
18:30:49.543 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
18:30:49.550 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
18:30:49.552 [main] ERROR com.alpine.marketing.App - ❌ Application failed: Task execution failed
java.lang.RuntimeException: Task execution failed
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:99)
	at com.alpine.marketing.cmd.SearchTaskRunner.run(SearchTaskRunner.java:50)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.alpine.marketing.App.main(App.java:29)
Caused by: java.lang.RuntimeException: Failed to execute pending tasks
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:172)
	at com.alpine.marketing.cmd.SearchTaskRunner.executeSearchTasks(SearchTaskRunner.java:91)
	... 21 common frames omitted
Caused by: java.lang.RuntimeException: Failed to update task completion
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:154)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository$$SpringCGLIB$$0.updateTaskCompletion(<generated>)
	at com.alpine.marketing.searchtask.service.SearchTaskService.executePendingTasks(SearchTaskService.java:160)
	... 22 common frames omitted
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [UPDATE search_task
SET status = ?, urls_found = ?, error_message = ?,
    completed = CURRENT_TIMESTAMP, updated = CURRENT_TIMESTAMP
WHERE id = ?
]
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:103)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:972)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1016)
	at org.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:1026)
	at com.alpine.marketing.searchtask.repository.SearchTaskRepository.updateTaskCompletion(SearchTaskRepository.java:144)
	... 34 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'completed' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$update$2(JdbcTemplate.java:977)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 38 common frames omitted
20:09:12.036 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 78827 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
20:09:12.038 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
20:09:12.401 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
20:09:12.408 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
20:09:12.427 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
20:09:12.427 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:09:12.427 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:09:12.427 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
20:09:12.427 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
20:09:12.427 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:09:12.427 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:09:12.488 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:09:12.488 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:09:12.489 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
20:09:12.489 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:09:12.489 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:09:12.489 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
20:09:12.505 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
20:09:12.505 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
20:09:12.506 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
20:09:12.506 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:09:13.281 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:09:13.313 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
20:09:13.313 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
20:09:13.314 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:09:13.314 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
20:09:13.314 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:09:13.317 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:09:13.317 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:09:13.317 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
20:09:13.317 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
20:09:13.317 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
20:09:13.318 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
20:09:13.318 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
20:09:13.318 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
20:09:13.318 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
20:09:14.842 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
20:09:14.843 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
20:09:14.848 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
20:09:14.848 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
20:09:14.849 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 3682ms (range: 1500-4000ms, momentum: 1.00)
20:09:18.537 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
20:09:18.538 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
20:09:18.539 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
20:09:18.539 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
20:09:18.575 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
20:09:18.664 [main] INFO  com.alpine.marketing.App - Started App in 6.839 seconds (process running for 7.325)
20:09:18.665 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 Search Task Runner started with profile: search-task
20:09:18.666 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: create with tag: blue
20:09:18.666 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
20:09:18.666 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
20:09:18.668 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
20:09:18.744 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e151e1f
20:09:18.745 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
20:09:18.798 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
20:09:18.830 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 4 search query templates for tag 'blue'
20:09:18.830 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 4 templates for tag 'blue'
20:09:18.841 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 11916 tasks (2979 schools × 4 templates)
20:09:20.594 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 11916 search tasks
20:09:20.594 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 11916 search tasks for tag 'blue'
20:09:20.594 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully for tag: 'blue'
20:09:20.594 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Search Task Runner Status
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Task Overview:
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total Tasks: 20853
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 20852
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 1
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Errors: 0
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completion Rate: {:.1f}%
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📝 Recommended Next Action:
20:09:20.606 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Run 'execute' command to process 20852 pending tasks
20:09:20.608 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
20:09:20.608 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
20:09:20.608 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
20:09:20.678 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
20:09:20.678 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
20:09:20.678 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
20:09:20.681 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
20:23:00.753 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 82245 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
20:23:00.755 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
20:23:01.102 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
20:23:01.110 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
20:23:01.130 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
20:23:01.131 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:23:01.131 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:23:01.131 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
20:23:01.131 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
20:23:01.131 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:23:01.131 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:23:01.186 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:23:01.186 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:23:01.186 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
20:23:01.186 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:23:01.186 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:23:01.187 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
20:23:01.202 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
20:23:01.202 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
20:23:01.202 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
20:23:01.202 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:23:01.853 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:23:01.888 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
20:23:01.888 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
20:23:01.889 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:23:01.889 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
20:23:01.889 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:23:01.892 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:23:01.892 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
20:23:01.892 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
20:23:03.233 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
20:23:03.234 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
20:23:03.239 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
20:23:03.239 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
20:23:03.240 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 2418ms (range: 1500-4000ms, momentum: 1.00)
20:23:05.663 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
20:23:05.663 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
20:23:05.664 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
20:23:05.664 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
20:23:05.704 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
20:23:05.793 [main] INFO  com.alpine.marketing.App - Started App in 5.235 seconds (process running for 5.562)
20:23:05.794 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 Search Task Runner started with profile: search-task
20:23:05.795 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: create with tag: blue
20:23:05.795 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Creating search tasks from templates for tag: 'blue'
20:23:05.795 [main] INFO  c.a.m.s.service.SearchTaskService - Creating search tasks from templates for tag: 'blue'
20:23:05.797 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
20:23:05.866 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4b4927e5
20:23:05.867 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
20:23:05.897 [main] INFO  c.a.m.s.repository.SchoolRepository - Found 2979 schools with valid web domains
20:23:05.918 [main] INFO  c.a.m.s.r.SearchQueryRepository - Found 4 search query templates for tag 'blue'
20:23:05.918 [main] INFO  c.a.m.s.service.SearchTaskService - Found 2979 schools and 4 templates for tag 'blue'
20:23:05.930 [main] INFO  c.a.m.s.service.SearchTaskService - Generated 11916 tasks (2979 schools × 4 templates)
20:23:07.674 [main] INFO  c.a.m.s.r.SearchTaskRepository - Successfully created 11916 search tasks
20:23:07.675 [main] INFO  c.a.m.s.service.SearchTaskService - Successfully created 11916 search tasks for tag 'blue'
20:23:07.675 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - ✅ Task creation completed successfully for tag: 'blue'
20:23:07.675 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Search Task Runner Status
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Task Overview:
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total Tasks: 11916
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 11916
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 0
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Errors: 0
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completion Rate: {:.1f}%
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📝 Recommended Next Action:
20:23:07.680 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Run 'execute' command to process 11916 pending tasks
20:23:07.682 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
20:23:07.682 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
20:23:07.683 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
20:23:07.753 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
20:23:07.754 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
20:23:07.754 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
20:23:07.757 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
20:26:01.943 [main] INFO  com.alpine.marketing.App - Starting App using Java 17.0.15 with PID 83147 (/Users/<USER>/Developer/com-alpine-marketing/out/production/classes started by killerkidbo in /Users/<USER>/Developer/com-alpine-marketing)
20:26:01.945 [main] INFO  com.alpine.marketing.App - The following 2 profiles are active: "lcl", "search-task"
20:26:02.316 [main] INFO  c.a.m.s.service.ApiKeyManagerService - Initialized API key manager with 12 keys
20:26:02.323 [main] INFO  c.a.m.s.service.GoogleApiService - Google Custom Search API client initialized
20:26:02.342 [main] INFO  c.a.m.s.config.SeleniumConfig - 🚀 Creating Chrome WebDriver with enhanced configuration and humanization
20:26:02.343 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:26:02.343 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:26:02.343 [main] INFO  c.a.m.s.config.SeleniumConfig - 📋 Platform: Platform: Mac OS X 15.6 (aarch64), Java: 17.0.15, Detected: ARM64
20:26:02.343 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Humanization enabled: true
20:26:02.343 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:26:02.343 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:26:02.399 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:26:02.399 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:26:02.400 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Starting Chrome version detection
20:26:02.400 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:26:02.400 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:26:02.400 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detecting Chrome version on macOS
20:26:02.424 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Found Chrome version in Info.plist: 138.0.7204.101
20:26:02.424 [main] INFO  c.a.m.s.config.ChromeVersionDetector - Detected Chrome version: 138.0.7204.101
20:26:02.424 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🔍 Searching for ChromeDriver compatible with Chrome 138.0.7204.101 (major: 138)
20:26:02.424 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloading file: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:26:03.086 [main] INFO  c.a.m.s.config.ChromeDriverManager - Downloaded 3172990 bytes from: https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
20:26:03.123 [main] INFO  c.a.m.s.config.ChromeDriverManager - Found major version match: 138.0.7152.0
20:26:03.123 [main] INFO  c.a.m.s.config.ChromeDriverManager - 🎯 Using major version match: 138.0.7152.0
20:26:03.123 [main] INFO  c.a.m.s.config.ChromeDriverManager - ChromeDriver already cached: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:26:03.123 [main] INFO  c.a.m.s.config.ChromeDriverManager - ✅ ChromeDriver ready: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver (version: 138.0.7152.0)
20:26:03.123 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 ChromeDriver path set automatically: src/main/resources/chromedriver/arm64/138.0.7152.0/chromedriver
20:26:03.127 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detecting system architecture: OS=mac os x, Arch=aarch64
20:26:03.127 [main] INFO  c.a.m.s.c.SystemArchitectureDetector - Detected architecture: macOS ARM64 (Apple Silicon)
20:26:03.127 [main] INFO  c.a.m.s.config.SeleniumConfig - 📱 Headless mode enabled
20:26:03.127 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome arguments
20:26:03.127 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome arguments applied
20:26:03.127 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Applied ARM64-specific Chrome optimizations
20:26:03.127 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Adding enhanced humanization Chrome preferences
20:26:03.128 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Enhanced humanization Chrome preferences applied
20:26:03.128 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Chrome options configured successfully with humanization features
20:26:04.321 [main] WARN  o.o.s.devtools.CdpVersionFinder - Unable to find CDP implementation matching 138
20:26:04.321 [main] WARN  o.o.selenium.chromium.ChromiumDriver - Unable to find version of CDP to use for 138.0.7204.101. You may need to include a dependency on a specific version of the CDP using something similar to `org.seleniumhq.selenium:selenium-devtools-v86:4.31.0` where the version ("v86") matches the version of the chromium-based browser you're using and the version number of the artifact is the same as Selenium's.
20:26:04.326 [main] INFO  c.a.m.s.config.SeleniumConfig - 🔧 Driver timeouts configured: pageLoad=45s, implicit=15s
20:26:04.326 [main] INFO  c.a.m.s.config.SeleniumConfig - 🤖 Applying WebDriver humanization features
20:26:04.327 [main] INFO  c.a.m.s.s.HumanBehaviorSimulator - Applying human-like delay: 2838ms (range: 1500-4000ms, momentum: 1.00)
20:26:07.170 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver humanization features applied
20:26:07.172 [main] INFO  c.a.m.s.config.SeleniumConfig - ✅ Chrome WebDriver created successfully (headless: true, humanization: true)
20:26:07.173 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Created initial WebDriver #1
20:26:07.173 [main] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool initialized with 1 drivers (max: 3)
20:26:07.215 [main] INFO  c.a.m.h.service.HunterIoService - ✅ Hunter.io Email Finder initialized - simplified mode
20:26:07.306 [main] INFO  com.alpine.marketing.App - Started App in 5.591 seconds (process running for 5.925)
20:26:07.308 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 🚀 Search Task Runner started with profile: search-task
20:26:07.308 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - Executing command: status with tag: blue
20:26:07.308 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📊 Search Task Runner Status
20:26:07.311 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
20:26:07.382 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e98032e
20:26:07.383 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📋 Task Overview:
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Total Tasks: 11916
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Pending: 11916
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completed: 0
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Quota Failed: 0
20:26:07.428 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Errors: 0
20:26:07.429 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Completion Rate: 0.0%
20:26:07.429 [main] INFO  c.a.marketing.cmd.SearchTaskRunner - 📝 Recommended Next Action:
20:26:07.429 [main] INFO  c.a.marketing.cmd.SearchTaskRunner -   Run 'execute' command to process 11916 pending tasks
20:26:07.431 [SpringApplicationShutdownHook] INFO  c.a.m.h.service.HunterIoService - HTTP client closed
20:26:07.431 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 Shutting down WebDriver pool...
20:26:07.432 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - 🔄 Shutting down WebDriver...
20:26:07.545 [SpringApplicationShutdownHook] INFO  c.a.m.s.config.SeleniumConfig - ✅ WebDriver shut down successfully
20:26:07.545 [SpringApplicationShutdownHook] INFO  c.a.m.s.service.WebDriverPoolService - 🚗 WebDriver pool shutdown complete. Quit 1 drivers.
20:26:07.545 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
20:26:07.565 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
