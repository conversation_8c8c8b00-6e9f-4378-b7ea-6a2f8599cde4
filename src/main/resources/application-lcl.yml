spring:
  banner:
    location: banner/lcl.txt
  datasource:
    url: *************************************************************************************************************************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver

# Search Task Configuration
searchtask:
  api-keys:
    - "AIzaSyCYMg-Co-pgFCv3Sg__TQALKfKEU3UeQe4"
    - "AIzaSyBJmN50SBqSORa6GGbdM8JNNpzG1HRT6lU"
    - "AIzaSyA8ABZ6ACBp9XNtYMpbAapt59eUVSYfv5c"
    - "AIzaSyCPRlDfdK45HZh78tWGbebJIm5zcQld6Nk"
    - "AIzaSyDmGxtqf394g1xIGnttV1kQrx-mEC9FS8I"
    - "AIzaSyB0-Nq_uOekN1R8rf108LQogTkkaaNQaRc"
    - "AIzaSyDk7EOR_wyTlyCqrrmM_h8B1JDS362QweA"
    - "AIzaSyB17A6z0FynGus2DNUVit_Qx8egZhk3-hc"
    - "AIzaSyDtpS5baOF8Wsh0qHptV6w-Ou34m6_28mw"
    - "AIzaSyBcKLnZnvj6_xY5kZ_Ke0cPBUZ4B3WYvRM"
    - "AIzaSyBXAyIuiWfihVZu0rWPJ8qurNsoIrW_W4k"
    - "AIzaSyC4Yy-ibqZupK628WidrxAedFmAAe8EqtU"
  cse-id: "227c4df8d0633487a"
  requests-per-second: 1.0

# Hunter.io Configuration
hunter:
  api-key: ****************************************
  email-finder-url: "https://api.hunter.io/v2/email-finder"
  email-verifier-url: "https://api.hunter.io/v2/email-verifier"
  delay-between-requests: 500 # 500ms delay between API calls
  processing:
    email-target-per-school: 3
    processing-limit: 60
    min-seed-emails: 2
    confidence-threshold: 0.6
