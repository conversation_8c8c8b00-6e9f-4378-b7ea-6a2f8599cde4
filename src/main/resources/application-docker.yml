spring:
  banner:
    location: banner/docker.txt
  datasource:
    url: jdbc:mysql://${DB_HOST:your-rds-endpoint.amazonaws.com}:${DB_PORT:3306}/${DB_NAME:email_scrapper}?useSSL=true&serverTimezone=UTC&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=UTF8
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver

# Google Search API configuration with quota management
searchtask:
  api-keys:
    - ${GCP_API_KEY_1:}
    - ${GCP_API_KEY_2:}
    - ${GCP_API_KEY_3:}
    - ${GCP_API_KEY_4:}
    - ${GCP_API_KEY_5:}
    - ${GCP_API_KEY_6:}
  cse-id: ${GCP_CSE_ID:057b28961b6dd4b81}
  requests-per-second: 1.0

# Selenium configuration optimized for Docker environment
selenium:
  headless: true
  page-load-timeout: 45s
  implicit-wait-timeout: 15s
  user-agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

  # WebDriver pool configuration for Docker
  pool:
    max-size: 2
    initial-size: 1

  # Performance settings for Docker environment
  performance:
    enable-images: false # Disable images for better performance in Docker
    enable-javascript: true
    enable-extensions: false
    enable-plugins: false
    memory-size: 2048

  # Driver management - use system ChromeDriver in Docker
  driver-management:
    auto-download: false # Use system ChromeDriver from Alpine package
    use-system-property: true
    download-timeout: 120s
    api-timeout: 30s
    enable-version-check: false # Skip version check in Docker
    enable-cache: false
    max-retries: 3

  # Humanization settings for Docker
  humanization:
    enabled: false # Disable for better performance in Docker environment

