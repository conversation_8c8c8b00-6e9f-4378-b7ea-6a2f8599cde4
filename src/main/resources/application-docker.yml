spring:
  banner:
    location: banner/docker.txt
  datasource:
    url: jdbc:mysql://${DB_HOST:your-rds-endpoint.amazonaws.com}:${DB_PORT:3306}/${DB_NAME:email_scrapper}?useSSL=true&serverTimezone=UTC&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=UTF8
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
# Google Search API configuration with quota management
searchtask:
  api-keys:
    - ${GCP_API_KEY_1}
    - ${GCP_API_KEY_2}
    - ${GCP_API_KEY_3}
    - ${GCP_API_KEY_4}
    - ${GCP_API_KEY_5}
    - ${GCP_API_KEY_6}
  cse-id: 057b28961b6dd4b81
  requests-per-second: 1.0

