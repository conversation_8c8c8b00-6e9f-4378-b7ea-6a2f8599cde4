# ChromeDriver Automatic Management

This directory contains automatically downloaded and cached ChromeDriver binaries for multi-architecture support.

## Directory Structure

```
chromedriver/
├── arm64/          # ChromeDriver binaries for macOS ARM64 (Apple Silicon M1/M2)
│   └── [version]/
│       └── chromedriver
└── linux64/        # ChromeDriver binaries for Linux x64
    └── [version]/
        └── chromedriver
```

## Automatic Download

The system automatically:

1. **Detects** your system architecture (ARM64 or Linux64)
2. **Identifies** your installed Chrome browser version
3. **Downloads** compatible ChromeDriver from Chrome for Testing API
4. **Caches** the driver locally in the appropriate architecture folder
5. **Configures** Selenium to use the correct driver

## Supported Platforms

- ✅ **macOS ARM64** (Apple Silicon M1/M2) - `mac-arm64`
- ✅ **Linux x64** - `linux64`
- ❌ **Windows** - Not supported (not required per project specs)

## Chrome for Testing API

Uses Google's official Chrome for Testing API for version compatibility:
- Base URL: `https://googlechromelabs.github.io/chrome-for-testing/`
- Known versions: `known-good-versions-with-downloads.json`
- Latest versions: `last-known-good-versions.json`

## Configuration

Configure automatic ChromeDriver management in `application.yml`:

```yaml
selenium:
  driver-management:
    auto-download: true              # Enable automatic driver download
    use-system-property: false       # Use existing system property if available
    download-timeout: PT120S         # Download timeout (2 minutes)
    api-timeout: PT30S               # API request timeout (30 seconds)
    enable-version-check: true       # Enable version compatibility checking
    enable-cache: true               # Enable local caching
    max-retries: 3                   # Maximum retry attempts
```

## Manual Cleanup

To clear cached drivers:

```bash
# Remove all cached drivers
rm -rf src/main/resources/chromedriver/*/

# Remove specific architecture
rm -rf src/main/resources/chromedriver/arm64/
rm -rf src/main/resources/chromedriver/linux64/
```

## Troubleshooting

### Version Mismatch Issues

1. **Clear cache** and let the system re-download
2. **Update Chrome** browser to latest version
3. **Check connectivity** to Chrome for Testing API

### Architecture Detection Issues

The system should automatically detect:
- **macOS ARM64**: `os.name=Mac OS X` + `os.arch=aarch64`
- **Linux x64**: `os.name=Linux` + `os.arch=amd64`

### Fallback Options

If automatic download fails:
1. Set `use-system-property: true` in configuration
2. Manually set `webdriver.chrome.driver` system property
3. Disable auto-download and use manual driver setup

## Version Compatibility

The system matches ChromeDriver versions with Chrome browser versions:
- **Exact match**: Same full version (e.g., 119.0.6045.105)
- **Major match**: Same major version (e.g., 119.x.x.x)
- **Fallback**: Latest stable if no match found

## Security Notes

- Downloaded drivers are verified for integrity
- Executable permissions are set automatically on Unix systems
- Downloads are cached to minimize network requests
- Uses secure HTTPS connections for all downloads

## Integration

The ChromeDriver management is integrated with:
- `SeleniumConfig.java` - Main driver creation
- `SystemArchitectureDetector.java` - Platform detection
- `ChromeVersionDetector.java` - Browser version detection
- `ChromeDriverManager.java` - Download and caching logic 