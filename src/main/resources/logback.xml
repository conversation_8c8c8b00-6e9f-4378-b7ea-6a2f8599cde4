<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
<!--            <fileNamePattern>./logs/app.%d{yyyy-MM-dd}.log</fileNamePattern>-->
             <fileNamePattern>/var/log/webapp/alpine/app/prd/tomcat.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>TRACE</level>
        </filter>
    </appender>

    <!-- Console appender with colored output -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="fileAppender" />
        <appender-ref ref="CONSOLE" />
    </root>

    <!-- Application specific logging -->
    <logger name="com.alpine.marketing" level="INFO" />

    <!-- Database connection logging -->
    <logger name="com.zaxxer.hikari" level="INFO" />

    <!-- Google API logging -->
    <logger name="com.google.api" level="INFO" />

    <logger name="com.google.api.client.http" level="INFO" />
<!--    <logger name="com.google.api.client.http" level="FINE" />-->

    <!-- HTTP client logging -->
    <logger name="org.apache.http" level="INFO" />

    <!-- Third party library noise reduction -->
    <logger name="org.gradle" level="WARN" />

</configuration>