# Google Search Runner Spring Boot Configuration
spring:
  application:
    name: marketing
  banner:
    location: banner/default.txt
    # HikariCP Connection Pool (Spring Boot auto-configures based on datasource settings)
    hikari:
      minimum-idle: 5 # Save resources when idle
      maximum-pool-size: 20 # Enough for most use cases
      connection-timeout: 30000 # Fail fast on connection issues
      idle-timeout: 300000 # Free up unused connections
      max-lifetime: 1200000 # Stay within safe DB connection limits
      leak-detection-threshold: 60000 # Great for catching leaks in dev

# Logging configuration for better debugging
logging:
  level:
    com.alpine.marketing: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Optimized Selenium Configuration for Enhanced Bot Detection Avoidance
selenium:
  headless: true # Set to false for testing/debugging
  page-load-timeout: 45s # Longer timeout for stability
  implicit-wait-timeout: 15s # Reasonable wait time

  # Enhanced user agent (periodically update this with current versions)
  user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

  # WebDriver pool configuration
  pool:
    max-size: 3
    initial-size: 1

  # Performance settings optimized for stealth vs speed balance
  performance:
    enable-images: true # Enable for realistic behavior (slight performance cost)
    enable-javascript: true # Required for most modern sites
    enable-extensions: false
    enable-plugins: false
    memory-size: 4096

  # Document processing settings
  document:
    max-size-mb: 10
    connection-timeout: 30s
    read-timeout: 60s

  # Driver management
  driver-management:
    auto-download: true
    use-system-property: false
    download-timeout: 120s
    api-timeout: 30s
    enable-version-check: true
    enable-cache: true
    max-retries: 3

  # ENHANCED HUMANIZATION - Optimized for maximum stealth
  humanization:
    enabled: true # Enable for production scraping

    # Page load delays - Realistic human processing time
    # Humans need 1.5-4 seconds to visually process a new page
    page-load-delay:
      min: 1500ms # 1.5 seconds
      max: 4000ms # 4 seconds

    # Action delays - Time between user interactions
    # Optimized based on human psychology research
    action-delay:
      min: 800ms # 0.8 seconds (quick but not robotic)
      max: 2500ms # 2.5 seconds (thoughtful pause)

    # Typing delays - Realistic human typing speed
    # Based on average typing speed: 40 WPM = ~100-200ms per character
    typing-delay:
      min: 80ms # Fast typist (burst speed)
      max: 250ms # Normal thinking speed

    # Enhanced scrolling behavior
    scroll-behavior:
      enabled: true
      max-scrolls: 4 # Increased for more realistic exploration
      scroll-percentage: 0.35 # Scroll 35% of viewport per action
      delay:
        min: 1200ms # Reading time between scrolls
        max: 3500ms # Extended reading for complex content

    # Mouse behavior (disabled in headless, but good for debugging)
    mouse-behavior:
      enabled: false # Enable for non-headless testing
      move-delay:
        min: 300ms
        max: 1200ms

server:
  port: 8080
