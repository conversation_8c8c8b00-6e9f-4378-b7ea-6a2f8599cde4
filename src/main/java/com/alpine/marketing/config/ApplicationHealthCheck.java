package com.alpine.marketing.config;

import com.alpine.marketing.searchtask.service.GoogleApiService;
import com.alpine.marketing.searchtask.service.SearchTaskService;
import com.alpine.marketing.seleniumscraper.service.WebDriverFactory;
import com.alpine.marketing.seleniumscraper.util.PlatformDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Application health check component that validates configuration and provides
 * helpful logging about service availability at startup.
 */
@Component
public class ApplicationHealthCheck {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationHealthCheck.class);

    @Autowired(required = false)
    private GoogleApiService googleApiService;

    @Autowired(required = false)
    private SearchTaskService searchTaskService;

    @Autowired(required = false)
    private WebDriverFactory webDriverFactory;

    @EventListener(ApplicationReadyEvent.class)
    public void performHealthCheck() {
        logger.info("🏥 Performing application health check...");

        checkPlatformCompatibility();
        checkWebDriverAvailability();
        checkGoogleApiConfiguration();

        logger.info("🏥 Health check completed. Application is ready.");
    }

    private void checkPlatformCompatibility() {
        PlatformDetector.Architecture arch = PlatformDetector.detectArchitecture();
        String platformInfo = PlatformDetector.getPlatformInfo();

        if (arch.isSupported()) {
            logger.info("✅ Platform compatibility: {} (Supported)", platformInfo);
        } else {
            logger.warn("⚠️ Platform compatibility: {} (Limited support - will attempt to use system ChromeDriver)",
                    platformInfo);
        }
    }

    private void checkWebDriverAvailability() {
        if (webDriverFactory == null) {
            logger.info("🚫 WebDriver: Disabled via configuration");
            return;
        }

        // Check Chrome browser availability
        boolean chromeAvailable = WebDriverFactory.isChromeBrowserAvailable();
        if (chromeAvailable) {
            logger.info("✅ Chrome Browser: Available");
        } else {
            logger.warn("⚠️ Chrome Browser: Not found in system");
            logger.warn("   Install Chrome or set CHROME_BIN environment variable");
            logger.warn("   Or disable WebDriver with --selenium.enabled=false");
        }

        logger.info("✅ WebDriver: Factory available and configured");
        if (!chromeAvailable) {
            logger.warn("⚠️ WebDriver: May fail to create drivers due to missing Chrome browser");
        }
    }

    private void checkGoogleApiConfiguration() {
        if (googleApiService == null) {
            logger.info("🚫 Google API: Disabled via configuration");
            return;
        }

        if (searchTaskService == null) {
            logger.warn("⚠️ Search Task Service: Not available");
            return;
        }

        if (searchTaskService.isServiceAvailable()) {
            logger.info("✅ Google API: Configured and ready - {}", searchTaskService.getServiceStatus());
        } else {
            logger.warn("⚠️ Google API: Not fully configured - {}", searchTaskService.getServiceStatus());
            logger.warn("   Search functionality will be limited until API keys are provided");
            logger.warn("   Set environment variables: GCP_API_KEY_1, GCP_API_KEY_2, etc.");

            // Debug environment variable loading
            debugEnvironmentVariables();
        }
    }

    private void debugEnvironmentVariables() {
        logger.debug("🔍 Environment variable debug:");
        for (int i = 1; i <= 6; i++) {
            String envVar = "GCP_API_KEY_" + i;
            String value = System.getenv(envVar);
            if (value != null && !value.trim().isEmpty()) {
                logger.debug("   {}: configured (length: {})", envVar, value.length());
            } else {
                logger.debug("   {}: not set or empty", envVar);
            }
        }

        String cseId = System.getenv("GCP_CSE_ID");
        if (cseId != null && !cseId.trim().isEmpty()) {
            logger.debug("   GCP_CSE_ID: configured (length: {})", cseId.length());
        } else {
            logger.debug("   GCP_CSE_ID: not set or empty");
        }
    }
}
