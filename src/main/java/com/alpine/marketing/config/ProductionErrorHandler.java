package com.alpine.marketing.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Production error handler that provides helpful guidance when the application
 * fails to start due to missing dependencies or configuration issues.
 */
@Component
public class ProductionErrorHandler {

    private static final Logger logger = LoggerFactory.getLogger(ProductionErrorHandler.class);

    @EventListener
    public void handleApplicationFailure(ApplicationFailedEvent event) {
        Throwable exception = event.getException();
        
        logger.error("🚨 Application failed to start: {}", exception.getMessage());
        
        // Analyze the exception and provide helpful guidance
        analyzeAndProvideGuidance(exception);
    }

    private void analyzeAndProvideGuidance(Throwable exception) {
        String message = exception.getMessage();
        String causeMessage = exception.getCause() != null ? exception.getCause().getMessage() : "";
        
        // WebDriver related issues
        if (message.contains("ChromeDriver") || message.contains("google-chrome") || 
            message.contains("WebDriver") || causeMessage.contains("Cannot run program 'google-chrome'")) {
            provideWebDriverGuidance();
        }
        
        // Google API related issues
        if (message.contains("Google API") || message.contains("API Keys: 0") || 
            message.contains("CSE ID: missing")) {
            provideGoogleApiGuidance();
        }
        
        // Database related issues
        if (message.contains("database") || message.contains("Connection") || 
            message.contains("SQLException")) {
            provideDatabaseGuidance();
        }
        
        // Bean creation issues
        if (message.contains("BeanCreationException") || message.contains("UnsatisfiedDependencyException")) {
            provideBeanCreationGuidance();
        }
        
        // General production guidance
        provideGeneralProductionGuidance();
    }

    private void provideWebDriverGuidance() {
        logger.error("💡 WebDriver Issue Detected:");
        logger.error("   This appears to be a Chrome/ChromeDriver compatibility issue.");
        logger.error("   ");
        logger.error("   🔧 Quick Fixes:");
        logger.error("   1. Disable WebDriver: --selenium.enabled=false");
        logger.error("   2. Install Chrome in Docker:");
        logger.error("      apt-get update && apt-get install -y google-chrome-stable");
        logger.error("   3. Set Chrome binary path:");
        logger.error("      export CHROME_BIN=/usr/bin/google-chrome");
        logger.error("   4. Use system ChromeDriver:");
        logger.error("      apt-get install -y chromium-chromedriver");
        logger.error("   ");
    }

    private void provideGoogleApiGuidance() {
        logger.error("💡 Google API Configuration Issue Detected:");
        logger.error("   This appears to be missing Google API credentials.");
        logger.error("   ");
        logger.error("   🔧 Quick Fixes:");
        logger.error("   1. Disable Search Tasks: --searchtask.enabled=false");
        logger.error("   2. Set environment variables in .env file:");
        logger.error("      GCP_API_KEY_1=your_api_key_here");
        logger.error("      GCP_CSE_ID=your_cse_id_here");
        logger.error("   3. Check .env file is properly mounted in Docker:");
        logger.error("      docker run --env-file=/path/to/.env ...");
        logger.error("   ");
    }

    private void provideDatabaseGuidance() {
        logger.error("💡 Database Connection Issue Detected:");
        logger.error("   This appears to be a database connectivity problem.");
        logger.error("   ");
        logger.error("   🔧 Quick Fixes:");
        logger.error("   1. Check database environment variables:");
        logger.error("      DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD");
        logger.error("   2. Verify database server is running and accessible");
        logger.error("   3. Check network connectivity to database");
        logger.error("   4. Verify database credentials are correct");
        logger.error("   ");
    }

    private void provideBeanCreationGuidance() {
        logger.error("💡 Bean Creation Issue Detected:");
        logger.error("   This appears to be a Spring configuration problem.");
        logger.error("   ");
        logger.error("   🔧 Quick Fixes:");
        logger.error("   1. Check for missing dependencies or configuration");
        logger.error("   2. Verify all required environment variables are set");
        logger.error("   3. Try disabling optional services:");
        logger.error("      --selenium.enabled=false --searchtask.enabled=false");
        logger.error("   ");
    }

    private void provideGeneralProductionGuidance() {
        logger.error("🚀 Production Deployment Guidance:");
        logger.error("   ");
        logger.error("   📋 Minimal Configuration (Database only):");
        logger.error("   docker run --env-file=.env \\");
        logger.error("     -e SELENIUM_ENABLED=false \\");
        logger.error("     -e SEARCHTASK_ENABLED=false \\");
        logger.error("     alpine --command=status");
        logger.error("   ");
        logger.error("   📋 Full Configuration:");
        logger.error("   1. Install Chrome: apt-get install -y google-chrome-stable");
        logger.error("   2. Set API keys in .env file");
        logger.error("   3. Configure database connection");
        logger.error("   4. Run with all services enabled");
        logger.error("   ");
        logger.error("   📋 Environment Variables Required:");
        logger.error("   - DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD (required)");
        logger.error("   - GCP_API_KEY_1, GCP_CSE_ID (optional, for search functionality)");
        logger.error("   - CHROME_BIN (optional, for WebDriver functionality)");
        logger.error("   ");
        logger.error("   📋 Service Control:");
        logger.error("   - SELENIUM_ENABLED=true/false (controls WebDriver)");
        logger.error("   - SEARCHTASK_ENABLED=true/false (controls Google API search)");
        logger.error("   ");
    }
}
