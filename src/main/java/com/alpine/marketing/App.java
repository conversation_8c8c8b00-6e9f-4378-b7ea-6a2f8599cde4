package com.alpine.marketing;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * A standalone Spring Boot microservice for collecting URLs from school domains
 * using Google Custom Search API.
 * Uses @ConfigurationPropertiesScan for type-safe configuration binding.
 */
@SpringBootApplication(scanBasePackages = {
		"com.alpine.marketing.searchtask",
		"com.alpine.marketing.seleniumscraper",
		"com.alpine.marketing.hunterio",
		"com.alpine.marketing.cmd",
})
@ConfigurationPropertiesScan({"com.alpine.marketing.searchtask.config",
		"com.alpine.marketing.seleniumscraper.config",
		"com.alpine.marketing.hunterio.config" })
public class App {
	private static final Logger logger = LoggerFactory.getLogger(App.class);

	public static void main(String[] args) {
		try {
			SpringApplication.run(App.class, args);
		} catch (Exception e) {
			logger.error("❌ Application failed: {}", e.getMessage(), e);
			System.exit(1);
		}
	}
}
