package com.alpine.marketing.searchtask.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for the searchtask module.
 * Manages Google API keys and other search-related settings.
 */
@ConfigurationProperties("searchtask")
public class SearchTaskProperties {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskProperties.class);

    private List<String> apiKeys = new ArrayList<>();

    private String cseId;

    private double requestsPerSecond;

    @PostConstruct
    public void logConfiguration() {
        logger.info("🔧 SearchTask Configuration Loaded:");
        logger.info("   API Keys: {} (filtered from environment variables)", apiKeys.size());
        logger.info("   CSE ID: {}", cseId != null ? "configured" : "missing");
        logger.info("   Requests per second: {}", requestsPerSecond);

        // Log environment variables for debugging
        String[] envVars = { "GCP_API_KEY_1", "GCP_API_KEY_2", "GCP_API_KEY_3", "GCP_API_KEY_4", "GCP_API_KEY_5",
                "GCP_API_KEY_6", "GCP_CSE_ID" };
        for (String envVar : envVars) {
            String value = System.getenv(envVar);
            if (value != null && !value.trim().isEmpty()) {
                logger.info("   Environment variable {}: {} (length: {})",
                        envVar, value.substring(0, Math.min(4, value.length())) + "...", value.length());
            } else {
                logger.warn("   Environment variable {} is not set or empty", envVar);
            }
        }

        if (!isValid()) {
            logger.error("❌ SearchTask configuration is invalid: {}", getSummary());
        } else {
            logger.info("✅ SearchTask configuration is valid");
        }
    }

    public List<String> getApiKeys() {
        return apiKeys;
    }

    public void setApiKeys(List<String> apiKeys) {
        this.apiKeys = new ArrayList<>();
        if (apiKeys != null) {
            // Filter out null, empty, or placeholder values
            for (String key : apiKeys) {
                if (key != null && !key.trim().isEmpty() && !key.equals("${GCP_API_KEY_1}")
                        && !key.equals("${GCP_API_KEY_2}") && !key.equals("${GCP_API_KEY_3}")
                        && !key.equals("${GCP_API_KEY_4}") && !key.equals("${GCP_API_KEY_5}")
                        && !key.equals("${GCP_API_KEY_6}")) {
                    this.apiKeys.add(key.trim());
                }
            }
        }
    }

    public String getCseId() {
        return cseId;
    }

    public void setCseId(String cseId) {
        this.cseId = cseId;
    }

    public double getRequestsPerSecond() {
        return requestsPerSecond;
    }

    public void setRequestsPerSecond(double requestsPerSecond) {
        this.requestsPerSecond = requestsPerSecond;
    }

    public boolean isValid() {
        return apiKeys != null && !apiKeys.isEmpty() &&
                cseId != null && !cseId.trim().isEmpty();
    }

    public String getSummary() {
        return String.format("API Keys: %d, CSE ID: %s, Rate: %.1f/sec",
                apiKeys.size(),
                cseId != null ? "configured" : "missing",
                requestsPerSecond);
    }
}