package com.alpine.marketing.searchtask.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for the searchtask module.
 * Manages Google API keys and other search-related settings.
 */
@ConfigurationProperties("searchtask")
public class SearchTaskProperties {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskProperties.class);

    private List<String> apiKeys = new ArrayList<>();

    private String cseId;

    private double requestsPerSecond;

    @PostConstruct
    public void logConfiguration() {
        logger.info("🔧 SearchTask Configuration Loaded:");
        logger.info("   API Keys: {} (filtered from environment variables)", apiKeys.size());
        logger.info("   CSE ID: {}", cseId != null ? "configured" : "missing");
        logger.info("   Requests per second: {}", requestsPerSecond);

        // Log environment variables for debugging
        String[] envVars = { "GCP_API_KEY_1", "GCP_API_KEY_2", "GCP_API_KEY_3", "GCP_API_KEY_4", "GCP_API_KEY_5",
                "GCP_API_KEY_6", "GCP_CSE_ID" };
        for (String envVar : envVars) {
            String value = System.getenv(envVar);
            if (value != null && !value.trim().isEmpty()) {
                logger.info("   Environment variable {}: {} (length: {})",
                        envVar, value.substring(0, Math.min(4, value.length())) + "...", value.length());
            } else {
                logger.warn("   Environment variable {} is not set or empty", envVar);
            }
        }

        if (!isValid()) {
            logger.error("❌ SearchTask configuration is invalid: {}", getSummary());
        } else {
            logger.info("✅ SearchTask configuration is valid");
        }
    }

    public List<String> getApiKeys() {
        return apiKeys;
    }

    public void setApiKeys(List<String> apiKeys) {
        this.apiKeys = new ArrayList<>();
        if (apiKeys != null) {
            // Filter out null, empty, or placeholder values
            for (String key : apiKeys) {
                if (key != null && !key.trim().isEmpty() &&
                        !key.startsWith("${") && !key.endsWith("}") &&
                        key.trim().length() > 10) { // API keys should be longer than 10 characters
                    this.apiKeys.add(key.trim());
                    logger.debug("Added API key: {}...", key.substring(0, Math.min(8, key.length())));
                } else {
                    logger.debug("Filtered out invalid API key: {}", key);
                }
            }
        }

        // If no API keys from configuration, try to load directly from environment
        if (this.apiKeys.isEmpty()) {
            logger.info("No API keys found in configuration, attempting direct environment variable loading...");
            String[] envVarNames = { "GCP_API_KEY_1", "GCP_API_KEY_2", "GCP_API_KEY_3",
                    "GCP_API_KEY_4", "GCP_API_KEY_5", "GCP_API_KEY_6" };

            for (String envVar : envVarNames) {
                String value = System.getenv(envVar);
                if (value != null && !value.trim().isEmpty() && value.trim().length() > 10) {
                    this.apiKeys.add(value.trim());
                    logger.info("Loaded API key from environment variable {}: {}...",
                            envVar, value.substring(0, Math.min(8, value.length())));
                }
            }
        }
    }

    public String getCseId() {
        return cseId;
    }

    public void setCseId(String cseId) {
        // Check if CSE ID is a placeholder or empty
        if (cseId != null && !cseId.trim().isEmpty() &&
                !cseId.startsWith("${") && !cseId.endsWith("}")) {
            this.cseId = cseId.trim();
        } else {
            // Try to load from environment variable directly
            String envCseId = System.getenv("GCP_CSE_ID");
            if (envCseId != null && !envCseId.trim().isEmpty()) {
                this.cseId = envCseId.trim();
                logger.info("Loaded CSE ID from environment variable: {}", this.cseId);
            } else {
                this.cseId = cseId; // Keep original value (might be default)
            }
        }
    }

    public double getRequestsPerSecond() {
        return requestsPerSecond;
    }

    public void setRequestsPerSecond(double requestsPerSecond) {
        this.requestsPerSecond = requestsPerSecond;
    }

    public boolean isValid() {
        return apiKeys != null && !apiKeys.isEmpty() &&
                cseId != null && !cseId.trim().isEmpty();
    }

    public String getSummary() {
        return String.format("API Keys: %d, CSE ID: %s, Rate: %.1f/sec",
                apiKeys.size(),
                cseId != null ? "configured" : "missing",
                requestsPerSecond);
    }
}