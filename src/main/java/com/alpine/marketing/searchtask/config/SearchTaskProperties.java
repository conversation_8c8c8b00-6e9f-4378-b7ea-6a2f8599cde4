package com.alpine.marketing.searchtask.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for the searchtask module.
 * Manages Google API keys and other search-related settings.
 */
@ConfigurationProperties("searchtask")
public class SearchTaskProperties {

    private List<String> apiKeys = new ArrayList<>();

    private String cseId;

    private double requestsPerSecond;

    public List<String> getApiKeys() {
        return apiKeys;
    }

    public void setApiKeys(List<String> apiKeys) {
        this.apiKeys = apiKeys != null ? apiKeys : new ArrayList<>();
    }

    public String getCseId() {
        return cseId;
    }

    public void setCseId(String cseId) {
        this.cseId = cseId;
    }

    public double getRequestsPerSecond() {
        return requestsPerSecond;
    }

    public void setRequestsPerSecond(double requestsPerSecond) {
        this.requestsPerSecond = requestsPerSecond;
    }

    public boolean isValid() {
        return apiKeys != null && !apiKeys.isEmpty() &&
                cseId != null && !cseId.trim().isEmpty();
    }

    public String getSummary() {
        return String.format("API Keys: %d, CSE ID: %s, Rate: %.1f/sec",
                apiKeys.size(),
                cseId != null ? "configured" : "missing",
                requestsPerSecond);
    }
}