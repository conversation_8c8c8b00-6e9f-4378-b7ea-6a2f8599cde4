package com.alpine.marketing.searchtask.config;

import com.alpine.marketing.searchtask.service.ApiKeyManagerService;
import com.alpine.marketing.searchtask.service.GoogleApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Search Task components.
 * Provides conditional bean creation based on Google API configuration availability.
 */
@Configuration
@EnableConfigurationProperties(SearchTaskProperties.class)
public class SearchTaskConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskConfiguration.class);

    @Bean
    @ConditionalOnProperty(name = "searchtask.enabled", havingValue = "true", matchIfMissing = true)
    public ApiKeyManagerService apiKeyManagerService(SearchTaskProperties properties) {
        ApiKeyManagerService service = new ApiKeyManagerService(properties);
        
        if (!service.hasAvailableKeys()) {
            logger.warn("⚠️ No valid Google API keys found. Search functionality will be limited. " +
                    "Please configure environment variables: GCP_API_KEY_1, GCP_API_KEY_2, etc.");
        } else {
            logger.info("✅ API Key Manager initialized with {} keys", service.getAvailableKeyCount());
        }
        
        return service;
    }

    @Bean
    @ConditionalOnProperty(name = "searchtask.enabled", havingValue = "true", matchIfMissing = true)
    public GoogleApiService googleApiService(SearchTaskProperties properties, ApiKeyManagerService apiKeyManager) {
        GoogleApiService service = new GoogleApiService(properties, apiKeyManager);
        
        if (!service.isConfigured()) {
            logger.warn("⚠️ Google API service not fully configured: {}. " +
                    "Search task execution will be disabled until configuration is complete.", 
                    service.getConfigSummary());
        } else {
            logger.info("✅ Google API service configured successfully: {}", service.getConfigSummary());
        }
        
        return service;
    }

    /**
     * Fallback configuration when Search Task functionality is disabled.
     */
    @Configuration
    @ConditionalOnProperty(name = "searchtask.enabled", havingValue = "false")
    static class DisabledSearchTaskConfiguration {
        
        private static final Logger logger = LoggerFactory.getLogger(DisabledSearchTaskConfiguration.class);
        
        public DisabledSearchTaskConfiguration() {
            logger.info("🚫 Search Task functionality is disabled via configuration. " +
                    "Google API search operations will not be available.");
        }
    }
}
