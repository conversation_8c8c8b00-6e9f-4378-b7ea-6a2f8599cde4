package com.alpine.marketing.searchtask.dto;

/**
 * Data Transfer Object for holding extracted LinkedIn profile information.
 * This is a self-contained DTO within the searchtask module.
 */
public class LinkedInProfileDto {

    private Integer searchResultId;
    private String firstName;
    private String lastName;
    private String role;
    private String linkedInUrl;
    private String schoolUrn;

    public Integer getSearchResultId() {
        return searchResultId;
    }

    public void setSearchResultId(Integer searchResultId) {
        this.searchResultId = searchResultId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getLinkedInUrl() {
        return linkedInUrl;
    }

    public void setLinkedInUrl(String linkedInUrl) {
        this.linkedInUrl = linkedInUrl;
    }

    public String getSchoolUrn() {
        return schoolUrn;
    }

    public void setSchoolUrn(String schoolUrn) {
        this.schoolUrn = schoolUrn;
    }
}