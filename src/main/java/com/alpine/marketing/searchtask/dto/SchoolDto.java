package com.alpine.marketing.searchtask.dto;

import java.util.Objects;

/**
 * Data Transfer Object representing a school record.
 * Maps to the 'school' table in the database.
 */
public class SchoolDto {

    private Integer urn;
    private String name;
    private String emailDomain;
    private String webDomain;

    public SchoolDto(Integer urn, String name, String emailDomain, String webDomain) {
        this.urn = urn;
        this.name = name;
        this.emailDomain = emailDomain;
        this.webDomain = webDomain;
    }

    public Integer getUrn() {
        return urn;
    }

    public void setUrn(Integer urn) {
        this.urn = urn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWebDomain() {
        return webDomain;
    }

    public void setWebDomain(String webDomain) {
        this.webDomain = webDomain;
    }

    public String getEmailDomain() { return emailDomain; }

    public void setEmailDomain(String emailDomain) { this.emailDomain = emailDomain; }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        SchoolDto schoolDto = (SchoolDto) o;
        return Objects.equals(urn, schoolDto.urn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(urn);
    }

    @Override
    public String toString() {
        return "SchoolDto{" +
                "urn=" + urn +
                ", name='" + name + '\'' +
                ", webDomain='" + webDomain + '\'' +
                '}';
    }
}