package com.alpine.marketing.searchtask.dto;

import java.util.Objects;

/**
 * Data Transfer Object representing a search task.
 * Maps to the 'search_task' table in the database.
 */
public class SearchTaskDto {

    private Integer id;
    private Integer searchQueryId;
    private Integer schoolUrn;
    private String searchSite;
    private String searchParams;
    private String finalQuery;
    private TaskStatus status;
    private String errorMessage;
    private int requiredNoOfResults;
    private Long totalResults;

    public SearchTaskDto() {
        this.status = TaskStatus.PENDING;
        this.requiredNoOfResults = 100;
    }

    public SearchTaskDto(Integer searchQueryId, Integer schoolUrn, String searchSite, String searchParams,
            int requiredNoOfResults) {
        this();
        this.searchQueryId = searchQueryId;
        this.schoolUrn = schoolUrn;
        this.searchSite = searchSite;
        this.searchParams = searchParams;
        this.requiredNoOfResults = requiredNoOfResults;
        this.finalQuery = buildFinalQuery(searchSite, searchParams);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSearchQueryId() {
        return searchQueryId;
    }

    public void setSearchQueryId(Integer searchQueryId) {
        this.searchQueryId = searchQueryId;
    }

    public Integer getSchoolUrn() {
        return schoolUrn;
    }

    public void setSchoolUrn(Integer schoolUrn) {
        this.schoolUrn = schoolUrn;
    }

    public String getSearchSite() {
        return searchSite;
    }

    public void setSearchSite(String searchSite) {
        this.searchSite = searchSite;
        this.finalQuery = buildFinalQuery(searchSite, searchParams);
    }

    public String getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(String searchParams) {
        this.searchParams = searchParams;
        this.finalQuery = buildFinalQuery(searchSite, searchParams);
    }

    public String getFinalQuery() {
        return finalQuery;
    }

    public void setFinalQuery(String finalQuery) {
        this.finalQuery = finalQuery;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public int getRequiredNoOfResults() {
        return requiredNoOfResults;
    }

    public void setRequiredNoOfResults(int requiredNoOfResults) {
        this.requiredNoOfResults = requiredNoOfResults;
    }

    public Long getTotalResults() {
        return totalResults;
    }

    public void setTotalResults(Long totalResults) {
        this.totalResults = totalResults;
    }

    private String buildFinalQuery(String site, String params) {
        if (site == null || params == null) {
            return null;
        }
        return String.format("site:%s %s", site.trim(), params.trim());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        SearchTaskDto that = (SearchTaskDto) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "SearchTaskDto{" +
                "id=" + id +
                ", searchQueryId=" + searchQueryId +
                ", schoolUrn=" + schoolUrn +
                ", searchSite='" + searchSite + '\'' +
                ", searchParams='" + searchParams + '\'' +
                ", finalQuery='" + finalQuery + '\'' +
                ", status='" + status + '\'' +
                ", requiredNoOfResults=" + requiredNoOfResults +
                '}';
    }
}