package com.alpine.marketing.searchtask.dto;

import java.util.Collections;
import java.util.List;

/**
 * Data Transfer Object for the response from our internal GoogleApiService.
 * Encapsulates the results of a search operation, including success/failure
 * status.
 */
public class GoogleSearchResponseDto {

    private final List<GoogleSearchItemDto> items;
    private final boolean success;
    private final String errorMessage;

    private GoogleSearchResponseDto(List<GoogleSearchItemDto> items, boolean success, String errorMessage) {
        this.items = items;
        this.success = success;
        this.errorMessage = errorMessage;
    }

    public static GoogleSearchResponseDto success(List<GoogleSearchItemDto> items) {
        return new GoogleSearchResponseDto(items, true, null);
    }

    public static GoogleSearchResponseDto failure(String errorMessage) {
        return new GoogleSearchResponseDto(Collections.emptyList(), false, errorMessage);
    }

    public List<GoogleSearchItemDto> getItems() {
        return items;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}