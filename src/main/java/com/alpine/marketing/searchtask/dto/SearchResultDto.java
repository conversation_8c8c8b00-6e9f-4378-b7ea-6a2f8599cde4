package com.alpine.marketing.searchtask.dto;

import java.util.Objects;

/**
 * Data Transfer Object representing a search result.
 * Maps to the 'search_result' table in the database.
 */
public class SearchResultDto {

    private Integer searchTaskId;
    private String url;
    private Integer schoolUrn;
    private String title;
    private String snippet;

    public SearchResultDto(Integer searchTaskId, String url, Integer schoolUrn, String title, String snippet) {
        this.searchTaskId = searchTaskId;
        this.url = url;
        this.schoolUrn = schoolUrn;
        this.title = title;
        this.snippet = snippet;
    }

    public Integer getSearchTaskId() {
        return searchTaskId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSchoolUrn() {
        return schoolUrn;
    }

    public void setSchoolUrn(Integer schoolUrn) {
        this.schoolUrn = schoolUrn;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSnippet() {
        return snippet;
    }

    public void setSnippet(String snippet) {
        this.snippet = snippet;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        SearchResultDto that = (SearchResultDto) o;
        return Objects.equals(searchTaskId, that.searchTaskId) &&
                Objects.equals(url, that.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(searchTaskId, url);
    }

    @Override
    public String toString() {
        return "SearchResultDto{" +
                "searchTaskId=" + searchTaskId +
                ", url='" + url + '\'' +
                ", schoolUrn=" + schoolUrn +
                ", title='" + title + '\'' +
                '}';
    }
}