package com.alpine.marketing.searchtask.dto;

/**
 * Data Transfer Object for a single item returned by a Google Custom Search.
 * Contains the core details needed for processing, including the link, title,
 * and snippet.
 */
public class GoogleSearchItemDto {

    private final String link;
    private final String title;
    private final String snippet;

    public GoogleSearchItemDto(String link, String title, String snippet) {
        this.link = link;
        this.title = title;
        this.snippet = snippet;
    }

    public String getLink() {
        return link;
    }

    public String getTitle() {
        return title;
    }

    public String getSnippet() {
        return snippet;
    }
}