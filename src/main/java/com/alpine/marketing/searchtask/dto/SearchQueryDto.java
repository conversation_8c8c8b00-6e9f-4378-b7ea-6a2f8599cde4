package com.alpine.marketing.searchtask.dto;

import java.util.Objects;

/**
 * Data Transfer Object representing a search query template.
 * Maps to the 'search_query' table in the database.
 */
public class SearchQueryDto {

    private Integer id;
    private String site;
    private String param;
    private String tag;
    private int requiredNoOfResults;

    public SearchQueryDto() {
        this.requiredNoOfResults = 100; // Default value
    }

    public SearchQueryDto(Integer id, String site, String param, String tag, int requiredNoOfResults) {
        this.id = id;
        this.site = site;
        this.param = param;
        this.tag = tag;
        this.requiredNoOfResults = requiredNoOfResults;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getRequiredNoOfResults() {
        return requiredNoOfResults;
    }

    public void setRequiredNoOfResults(int requiredNoOfResults) {
        this.requiredNoOfResults = requiredNoOfResults;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        SearchQueryDto that = (SearchQueryDto) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "SearchQueryDto{" +
                "id=" + id +
                ", site='" + site + '\'' +
                ", param='" + param + '\'' +
                ", tag='" + tag + '\'' +
                ", requiredNoOfResults=" + requiredNoOfResults +
                '}';
    }
}