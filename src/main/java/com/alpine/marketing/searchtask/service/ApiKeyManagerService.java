package com.alpine.marketing.searchtask.service;

import com.alpine.marketing.searchtask.config.SearchTaskProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Service for managing Google API keys with rotation and cooldown mechanism.
 * Provides thread-safe access to API keys with failure tracking.
 */
@Service
public class ApiKeyManagerService {

    private static final Logger logger = LoggerFactory.getLogger(ApiKeyManagerService.class);

    private final ConcurrentLinkedQueue<String> availableKeys;
    private final Set<String> failedKeys;
    private final SearchTaskProperties properties;
    private volatile String currentApiKey = null;

    public ApiKeyManagerService(SearchTaskProperties properties) {
        this.properties = properties;
        this.availableKeys = new ConcurrentLinkedQueue<>();
        this.failedKeys = ConcurrentHashMap.newKeySet();

        initializeKeys();
    }

    private void initializeKeys() {
        if (properties.getApiKeys() == null || properties.getApiKeys().isEmpty()) {
            logger.warn("No API keys configured in SearchTaskProperties");
            return;
        }

        for (String key : properties.getApiKeys()) {
            if (key != null && !key.trim().isEmpty()) {
                availableKeys.offer(key.trim());
            }
        }

        logger.info("Initialized API key manager with {} keys", availableKeys.size());
    }

    /**
     * Gets the currently active API key. If no key is active (or the previous one
     * failed), it polls a new one from the queue and makes it the active "sticky"
     * key.
     *
     * @return The currently active API key.
     * @throws AllApiKeysExhaustedException if the active key is null and the queue
     *                                      is empty.
     */
    public String getNextKey() throws AllApiKeysExhaustedException {
        // If we already have a sticky key that hasn't failed, use it.
        if (this.currentApiKey != null) {
            return this.currentApiKey;
        }

        // If there's no sticky key, poll a new one from the queue.
        this.currentApiKey = availableKeys.poll();

        if (this.currentApiKey == null) {
            logger.error("No API keys available in the queue to become the new active key.");
            throw new AllApiKeysExhaustedException("All API keys from the queue have been exhausted");
        }

        logger.info("Setting new active API key: {}...", getApiKeyForLogging(this.currentApiKey));
        return this.currentApiKey;
    }

    /**
     * Reports that an API key has failed due to quota exhaustion.
     * Adds the key to the failed set and clears the "sticky" key if it was the one
     * that failed.
     *
     * @param failedKey The API key that hit its quota limit.
     */
    public void reportQuotaFailure(String failedKey) {
        if (failedKey == null) {
            return;
        }

        // Add the key to the set of failed keys to prevent it from being used again
        // until reset.
        boolean wasNew = failedKeys.add(failedKey);

        if (wasNew) {
            logger.warn("API key failed due to quota exhaustion and has been moved to the failed set: {}...",
                    getApiKeyForLogging(failedKey));
        }

        // If the key that failed is our current sticky key, clear it.
        // This will force getNextKey() to poll a new one on the next call.
        if (failedKey.equals(this.currentApiKey)) {
            this.currentApiKey = null;
            logger.info("Cleared the active sticky key. A new key will be fetched on the next request.");
        }
    }

    public int getAvailableKeyCount() {
        return availableKeys.size();
    }

    public int getFailedKeyCount() {
        return failedKeys.size();
    }

    public boolean hasAvailableKeys() {
        return !availableKeys.isEmpty();
    }

    public String getStatusSummary() {
        return String.format("API Keys - Available: %d, Failed: %d, Total Configured: %d",
                availableKeys.size(), failedKeys.size(), properties.getApiKeys().size());
    }

    public void resetFailedKeys() {
        for (String failedKey : failedKeys) {
            availableKeys.offer(failedKey);
        }

        int resetCount = failedKeys.size();
        failedKeys.clear();

        logger.info("Reset {} failed keys back to available status", resetCount);
    }

    private String getApiKeyForLogging(String key) {
        if (key == null)
            return "null";
        return key.substring(0, Math.min(4, key.length()));
    }

    public static class AllApiKeysExhaustedException extends Exception {
        public AllApiKeysExhaustedException(String message) {
            super(message);
        }
    }
}