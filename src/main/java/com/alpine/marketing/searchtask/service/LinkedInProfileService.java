package com.alpine.marketing.searchtask.service;

import com.alpine.marketing.searchtask.dto.LinkedInProfileDto;
import com.alpine.marketing.searchtask.repository.LinkedInProfileRepository;
import com.alpine.marketing.searchtask.repository.SearchResultRepository;
import com.alpine.marketing.searchtask.dto.SearchResultDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service for processing LinkedIn search results and extracting profile data.
 * Orchestrates the extraction and storage of profile information.
 */
@Service
public class LinkedInProfileService {

    private static final Logger logger = LoggerFactory.getLogger(LinkedInProfileService.class);

    private final LinkedInProfileRepository linkedInProfileRepository;
    private final SearchResultRepository searchResultRepository;

    public LinkedInProfileService(LinkedInProfileRepository linkedInProfileRepository,
            SearchResultRepository searchResultRepository) {
        this.linkedInProfileRepository = linkedInProfileRepository;
        this.searchResultRepository = searchResultRepository;
    }

    public void processCompletedLinkedInTasks(String tag) {
        logger.info("Starting LinkedIn profile processing for tag '{}'", tag);
        List<SearchResultDto> unprocessedResults = searchResultRepository.findUnprocessedLinkedInResults(tag);

        if (unprocessedResults.isEmpty()) {
            logger.info("No unprocessed LinkedIn results found to process.");
            return;
        }

        logger.info("Found {} unprocessed LinkedIn results to process.", unprocessedResults.size());

        int profilesExtracted = 0;
        for (SearchResultDto result : unprocessedResults) {
            try {
                LinkedInProfileDto profile = extractProfileFromSearchResult(result);
                if (profile != null) {
                    linkedInProfileRepository.saveProfile(profile);
                    profilesExtracted++;
                    logger.info("✅ Extracted profile for {}: {}", profile.getLinkedInUrl(), profile.getRole());
                } else {
                    logger.warn("⏭️ Could not extract a valid profile from title: '{}'", result.getTitle());
                }

                Integer searchResultId = searchResultRepository.findIdByUrlAndTaskId(result.getUrl(),
                        result.getSearchTaskId());
                if (searchResultId != null) {
                    searchResultRepository.markAsScanned(searchResultId);
                } else {
                    logger.warn("Could not find search_result ID for URL '{}' to mark as scanned.", result.getUrl());
                }

            } catch (Exception e) {
                logger.error("❌ Failed to process result with URL {}: {}", result.getUrl(), e.getMessage(), e);
            }
        }

        logger.info("LinkedIn processing complete: {} profiles extracted from {} results.",
                profilesExtracted, unprocessedResults.size());
    }

    private LinkedInProfileDto extractProfileFromSearchResult(SearchResultDto result) {
        if (result == null || result.getTitle() == null) {
            return null;
        }

        String title = result.getTitle().replace("| LinkedIn", "").trim();

        String[] parts = title.split("\\s-\\s");

        if (parts.length < 1) {
            return null;
        }

        String fullName = parts[0].trim();
        String[] nameParts = fullName.split("\\s+");
        if (nameParts.length < 2) {
            return null;
        }

        String firstName = capitalize(nameParts[0]);
        String lastName = capitalize(nameParts[1]);

        String role = (parts.length > 1) ? parts[1].trim() : "Role not specified";

        LinkedInProfileDto profile = new LinkedInProfileDto();
        profile.setFirstName(firstName);
        profile.setLastName(lastName);
        profile.setRole(role);
        profile.setLinkedInUrl(result.getUrl());
        profile.setSchoolUrn(result.getSchoolUrn().toString());
        profile.setSearchResultId(
                searchResultRepository.findIdByUrlAndTaskId(result.getUrl(), result.getSearchTaskId()));

        return profile;
    }

    private String capitalize(String word) {
        if (word == null || word.isEmpty()) {
            return word;
        }
        return Character.toUpperCase(word.charAt(0)) + word.substring(1).toLowerCase();
    }
}