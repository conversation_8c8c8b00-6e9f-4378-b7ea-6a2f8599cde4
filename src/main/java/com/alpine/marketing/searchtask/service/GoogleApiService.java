package com.alpine.marketing.searchtask.service;

import com.alpine.marketing.searchtask.config.SearchTaskProperties;
import com.alpine.marketing.searchtask.dto.SearchTaskDto;
import com.alpine.marketing.searchtask.dto.TaskStatus;
import com.alpine.marketing.searchtask.dto.SearchResultDto;
import com.alpine.marketing.searchtask.repository.SearchTaskRepository;
import com.alpine.marketing.searchtask.repository.SearchResultRepository;
import com.alpine.marketing.searchtask.service.ApiKeyManagerService.AllApiKeysExhaustedException;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.customsearch.v1.CustomSearchAPI;
import com.google.api.services.customsearch.v1.model.Result;
import com.google.api.services.customsearch.v1.model.Search;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for making Google Custom Search API calls.
 * Handles HTTP requests to Google with API key rotation, error handling, and
 * dynamic, stateful pagination.
 */
@Service
public class GoogleApiService {

    private static final Logger logger = LoggerFactory.getLogger(GoogleApiService.class);
    private static final int MAX_RESULTS_PER_PAGE = 10;

    private final SearchTaskProperties properties;
    private final ApiKeyManagerService apiKeyManager;
    private CustomSearchAPI customSearchAPI;
    private long lastRequestTime = 0;

    public GoogleApiService(SearchTaskProperties properties, ApiKeyManagerService apiKeyManager) {
        this.properties = properties;
        this.apiKeyManager = apiKeyManager;
        initializeApiClient();
    }

    private void initializeApiClient() {
        this.customSearchAPI = new CustomSearchAPI.Builder(
                new NetHttpTransport(),
                GsonFactory.getDefaultInstance(),
                null)
                .setApplicationName("SearchTask Runner")
                .build();

        logger.info("Google Custom Search API client initialized");
    }

    /**
     * Executes a search for a given task, using dynamic pagination.
     * The process is stateful and resumable. It determines the next starting index
     * by counting existing results and uses the total results from the first API
     * call to avoid unnecessary fetches.
     */
    public void executeSearch(SearchTaskDto task, SearchTaskRepository taskRepo, SearchResultRepository resultRepo) {
        if (task == null || task.getId() == null) {
            throw new IllegalArgumentException("SearchTaskDto and its ID cannot be null");
        }

        try {
            // Dynamically determine where to start based on already saved results.
            int savedResultsCount = resultRepo.countByTaskId(task.getId());
            long effectiveTotalResults = task.getTotalResults() != null ? task.getTotalResults() : Long.MAX_VALUE;
            long requiredResults = task.getRequiredNoOfResults();

            // This is the main loop condition: continue as long as we need more results
            // AND we haven't exceeded what the API says are available.
            while (savedResultsCount < requiredResults && savedResultsCount < effectiveTotalResults) {
                int startIndex = savedResultsCount + 1;

                if (startIndex > requiredResults) {
                    logger.info("Next start index {} exceeds Google's API limit. Stopping task {}.", startIndex,
                            task.getId());
                    break;
                }

                logger.info(
                        "Task {}: Fetching page starting at index {}. (Saved: {}, Required: {}, Total Available: {})",
                        task.getId(), startIndex, savedResultsCount, requiredResults,
                        task.getTotalResults() != null ? task.getTotalResults() : "Unknown");

                PageFetchResponse response = fetchSinglePage(task, startIndex);

                // If this was the first fetch for the task, we now have the total results.
                if (task.getTotalResults() == null) {
                    effectiveTotalResults = response.totalResults();
                    taskRepo.updateTotalResults(task.getId(), effectiveTotalResults);
                    task.setTotalResults(effectiveTotalResults); // Update in-memory DTO
                }

                if (response.items().isEmpty()) {
                    logger.info("Task {}: Received 0 items, indicating the end of results.", task.getId());
                    break; // No more results to fetch.
                }

                // Immediately save the newly fetched items.
                resultRepo.saveBatch(response.items());

                // The task is now officially started and has made progress.
                if (task.getStatus() == TaskStatus.PENDING) {
                    taskRepo.updateTaskCompletion(task.getId(), TaskStatus.PARTIALLY_COMPLETED, null);
                    task.setStatus(TaskStatus.PARTIALLY_COMPLETED); // Update in-memory DTO
                }

                // Recalculate the count for the next iteration's `while` check.
                savedResultsCount = resultRepo.countByTaskId(task.getId());

                // If the API returned fewer items than we asked for, it must be the last page.
                if (response.items().size() < MAX_RESULTS_PER_PAGE) {
                    logger.info("Task {}: API returned fewer items than requested, assuming end of results.",
                            task.getId());
                    break;
                }
            }

            // After the loop, mark the task as fully completed.
            taskRepo.updateTaskCompletion(task.getId(), TaskStatus.COMPLETED, null);
            logger.info("✅ Task {} finished successfully. Total results saved: {}", task.getId(), savedResultsCount);

        } catch (AllApiKeysExhaustedException e) {
            logger.error("⚠️ All API keys exhausted for task {}. It will be resumed later. Error: {}",
                    task.getId(), e.getMessage());
            // The task status is left as PENDING or PARTIALLY_COMPLETED, which is the
            // desired state for a resumable failure.
        } catch (Exception e) {
            logger.error("❌ Task {} failed with an unrecoverable error: {}", task.getId(), e.getMessage(), e);
            taskRepo.updateTaskCompletion(task.getId(), TaskStatus.ERROR, e.getMessage());
        }
    }

    private record PageFetchResponse(List<SearchResultDto> items, long totalResults) {
    }

    private PageFetchResponse fetchSinglePage(SearchTaskDto task, int startIndex)
            throws AllApiKeysExhaustedException {
        String currentApiKey = null;
        Exception lastException = null;

        while (apiKeyManager.hasAvailableKeys()) {
            try {
                currentApiKey = apiKeyManager.getNextKey();
                logger.info("Attempting page fetch (start: {}) with API key: {}...",
                        startIndex, currentApiKey.substring(0, Math.min(4, currentApiKey.length())));

                enforceRateLimit();

                Search searchResult = customSearchAPI.cse().list()
                        .setKey(currentApiKey)
                        .setCx(properties.getCseId())
                        .setQ(task.getFinalQuery())
                        .setStart((long) startIndex)
                        .setNum(MAX_RESULTS_PER_PAGE)
                        .execute();

                List<SearchResultDto> items = extractItems(searchResult, task);
                long totalResults = extractTotalResults(searchResult);

                logger.info("Page fetch successful (start: {}): {} items found. API total results: {}", startIndex,
                        items.size(), totalResults);

                return new PageFetchResponse(items, totalResults);

            } catch (Exception e) {
                lastException = e;

                if (isQuotaError(e)) {
                    logger.warn("Quota exhaustion for API key, rotating to next key. Message: {}", e.getMessage());
                    apiKeyManager.reportQuotaFailure(currentApiKey);
                } else {
                    logger.error("Page fetch failed (start: {}) with a non-quota error: {}", startIndex,
                            e.getMessage());
                    throw new RuntimeException("Page fetch failed: " + e.getMessage(), e);
                }
            }
        }

        throw new AllApiKeysExhaustedException("All API keys have been exhausted. Last error: "
                + (lastException != null ? lastException.getMessage() : "N/A"));
    }

    /**
     * Safely extracts the total number of results from the Google Search API
     * response using the client library's typed models.
     *
     * @param searchResult The Search object returned by the API.
     * @return The total number of results, or 0L if not found.
     */
    private long extractTotalResults(Search searchResult) {
        if (searchResult == null || searchResult.getQueries() == null) {
            return 0L;
        }

        // The "request" query object contains the totalResults for the entire query.
        Object requestListObject = searchResult.getQueries().get("request");

        if (requestListObject instanceof List<?> requestList && !requestList.isEmpty()) {
            if (requestList.get(0) instanceof com.google.api.client.json.GenericJson requestInfo) {
                Object totalResultsObject = requestInfo.get("totalResults");

                if (totalResultsObject != null) {
                    try {
                        return Long.parseLong(totalResultsObject.toString());
                    } catch (NumberFormatException e) {
                        logger.warn("Could not parse totalResults from API response: '{}'", totalResultsObject);
                    }
                }
            }
        }

        return 0L;
    }

    private List<SearchResultDto> extractItems(Search searchResult, SearchTaskDto task) {
        List<SearchResultDto> items = new ArrayList<>();

        if (searchResult.getItems() != null) {
            for (Result item : searchResult.getItems()) {
                if (item.getLink() != null && !item.getLink().trim().isEmpty()) {
                    items.add(new SearchResultDto(
                            task.getId(),
                            item.getLink().trim(),
                            task.getSchoolUrn(),
                            item.getTitle(),
                            item.getSnippet()));
                }
            }
        }

        return items;
    }

    private boolean isQuotaError(Exception e) {
        if (e == null || e.getMessage() == null) {
            return false;
        }

        String message = e.getMessage().toLowerCase();
        return message.contains("quota") ||
                message.contains("limit exceeded") ||
                message.contains("429") ||
                message.contains("rate limit") ||
                message.contains("daily limit");
    }

    private void enforceRateLimit() {
        long timeSinceLastRequest = System.currentTimeMillis() - lastRequestTime;
        long requiredDelay = (long) (1000.0 / properties.getRequestsPerSecond());

        if (timeSinceLastRequest < requiredDelay) {
            long sleepTime = requiredDelay - timeSinceLastRequest;
            try {
                logger.info("Rate limiting: sleeping {}ms", sleepTime);
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Rate limiting interrupted", e);
            }
        }

        lastRequestTime = System.currentTimeMillis();
    }

    public boolean isConfigured() {
        return properties.isValid() && apiKeyManager.hasAvailableKeys();
    }

    public String getConfigSummary() {
        return String.format("%s, %s", properties.getSummary(), apiKeyManager.getStatusSummary());
    }
}