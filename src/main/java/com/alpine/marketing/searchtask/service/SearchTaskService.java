package com.alpine.marketing.searchtask.service;

import com.alpine.marketing.searchtask.dto.GoogleSearchResponseDto;
import com.alpine.marketing.searchtask.dto.GoogleSearchItemDto;
import com.alpine.marketing.searchtask.dto.SchoolDto;
import com.alpine.marketing.searchtask.dto.SearchQueryDto;
import com.alpine.marketing.searchtask.dto.SearchResultDto;
import com.alpine.marketing.searchtask.dto.SearchTaskDto;
import com.alpine.marketing.searchtask.dto.TaskStatus;
import com.alpine.marketing.searchtask.repository.SchoolRepository;
import com.alpine.marketing.searchtask.repository.SearchQueryRepository;
import com.alpine.marketing.searchtask.repository.SearchResultRepository;
import com.alpine.marketing.searchtask.repository.SearchTaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Orchestrator service for search task management.
 * Coordinates between repositories and services to manage the search workflow.
 * Integrates with LinkedIn processing for seamless data flow.
 */
@Service
public class SearchTaskService {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskService.class);

    private final SchoolRepository schoolRepository;
    private final SearchQueryRepository searchQueryRepository;
    private final SearchTaskRepository searchTaskRepository;
    private final SearchResultRepository searchResultRepository;
    private final GoogleApiService googleApiService;
    private final LinkedInProfileService linkedInProfileService;

    public SearchTaskService(SchoolRepository schoolRepository,
            SearchQueryRepository searchQueryRepository,
            SearchTaskRepository searchTaskRepository,
            SearchResultRepository searchResultRepository,
            GoogleApiService googleApiService,
            LinkedInProfileService linkedInProfileService) {
        this.schoolRepository = schoolRepository;
        this.searchQueryRepository = searchQueryRepository;
        this.searchTaskRepository = searchTaskRepository;
        this.searchResultRepository = searchResultRepository;
        this.googleApiService = googleApiService;
        this.linkedInProfileService = linkedInProfileService;
    }

    public void createTasksFromTemplates() {
        try {
            List<SearchQueryDto> templates = searchQueryRepository.findAll();

            if (templates.isEmpty()) {
                logger.warn("No search query templates found - cannot create tasks");
                return;
            }

            logger.info("Found {} templates to process", templates.size());

            List<SearchTaskDto> tasksToCreate = new ArrayList<>();

            for (SearchQueryDto template : templates) {
                if (isSchoolSpecificTemplate(template)) {
                    List<SchoolDto> schools = schoolRepository.findAllWithWebDomains();

                    if (schools.isEmpty()) {
                        logger.warn("Template {} requires schools but no schools found with web domains",
                                template.getId());
                        continue;
                    }

                    logger.info("Creating school-specific tasks for template {} across {} schools",
                            template.getId(), schools.size());

                    for (SchoolDto school : schools) {
                        SearchTaskDto task = createTaskFromSchoolTemplate(school, template);
                        tasksToCreate.add(task);
                    }
                } else {
                    logger.info("Creating generic task for template {} (no school association)", template.getId());
                    SearchTaskDto task = createGenericTask(template);
                    tasksToCreate.add(task);
                }
            }

            logger.info("Generated {} tasks from {} templates", tasksToCreate.size(), templates.size());

            searchTaskRepository.createBatch(tasksToCreate);

            logger.info("Successfully created {} search tasks", tasksToCreate.size());

        } catch (Exception e) {
            logger.error("Failed to create tasks from templates: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create tasks from templates", e);
        }
    }

    public void executePendingTasks(String tag) {
        if (!googleApiService.isConfigured()) {
            String error = "Google API service not configured: " + googleApiService.getConfigSummary();
            logger.error(error);
            throw new RuntimeException(error);
        }

        try {
            List<SearchTaskDto> processableTasks = searchTaskRepository.findProcessableTasks(tag);

            if (processableTasks.isEmpty()) {
                logger.info("No processable (PENDING or PARTIALLY_COMPLETED) tasks found for tag '{}'",
                        tag);
                return;
            }

            logger.info("Found {} processable tasks to execute for tag '{}'",
                    processableTasks.size(), tag);

            int tasksProcessed = 0;

            for (SearchTaskDto task : processableTasks) {
                try {
                    logger.info("Executing task {} (status: {}, total_results: {})",
                            task.getId(), task.getStatus(),
                            task.getTotalResults() != null ? task.getTotalResults() : "N/A");

                    googleApiService.executeSearch(task, searchTaskRepository, searchResultRepository);

                    tasksProcessed++;

                } catch (Exception e) {
                    // This outer catch is for unexpected infrastructure errors, not API errors,
                    // which are handled inside the GoogleApiService.
                    searchTaskRepository.updateTaskCompletion(task.getId(), TaskStatus.ERROR, e.getMessage());
                    logger.error("❌ Task {} failed with an unexpected infrastructure error: {}", task.getId(),
                            e.getMessage(), e);
                }
            }

            logger.info("Task execution loop finished for tag '{}': {} tasks were processed.",
                    tag, tasksProcessed);

        } catch (Exception e) {
            logger.error("Failed to execute processable tasks for tag '{}': {}", tag, e.getMessage(), e);
            throw new RuntimeException("Failed to execute processable tasks", e);
        }
    }

    public void executeProcessLinkedIn(String tag) {
        logger.info("🚀 Initiating decoupled LinkedIn profile processing...");
        linkedInProfileService.processCompletedLinkedInTasks(tag);
        logger.info("✅ Decoupled LinkedIn profile processing finished.");
    }

    private boolean isSchoolSpecificTemplate(SearchQueryDto template) {
        String site = template.getSite() != null ? template.getSite() : "";
        String param = template.getParam() != null ? template.getParam() : "";

        // A template is school-specific if it contains any {{...}} placeholders.
        return site.contains("{{school") || param.contains("{{school");
    }

    private SearchTaskDto createTaskFromSchoolTemplate(SchoolDto school, SearchQueryDto template) {
        String resolvedSite = replacePlaceholders(template.getSite(), school);
        String resolvedParams = replacePlaceholders(template.getParam(), school);

        return new SearchTaskDto(
                template.getId(),
                school.getUrn(),
                resolvedSite,
                resolvedParams,
                template.getRequiredNoOfResults());
    }

    private SearchTaskDto createGenericTask(SearchQueryDto template) {
        return new SearchTaskDto(
                template.getId(),
                0,
                template.getSite(),
                template.getParam(),
                template.getRequiredNoOfResults());
    }

    private String replacePlaceholders(String template, SchoolDto school) {
        if (template == null) {
            return null;
        }

        String result = template;

        if (school.getName() != null) {
            result = result.replace("{{school_name}}", school.getName());
        }

        if (school.getWebDomain() != null) {
            result = result.replace("{{school_web_domain}}", school.getWebDomain());
        }

        if (school.getEmailDomain() != null) {
            result = result.replace("{{school_email_domain}}", school.getEmailDomain());
        } else {
            result = result.replace("{{school_email_domain}}", school.getWebDomain());
        }

        result = result.replace("{{school_urn}}", school.getUrn().toString());

        return result;
    }

    public SearchTaskRepository.TaskStats getTaskStats() {
        return searchTaskRepository.getTaskStats();
    }

    public int resetQuotaFailedTasks() {
        return searchTaskRepository.resetQuotaFailedTasks();
    }
}