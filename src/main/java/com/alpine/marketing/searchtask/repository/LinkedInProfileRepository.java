package com.alpine.marketing.searchtask.repository;

import com.alpine.marketing.searchtask.dto.LinkedInProfileDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Repository for handling database operations related to LinkedIn profiles.
 */
@Repository
public class LinkedInProfileRepository {

    private static final Logger logger = LoggerFactory.getLogger(LinkedInProfileRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public LinkedInProfileRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Transactional
    public void saveProfile(LinkedInProfileDto profile) {
        if (profile == null || profile.getLinkedInUrl() == null || profile.getLinkedInUrl().trim().isEmpty()) {
            logger.warn("Attempted to save a profile with a null or empty LinkedIn URL.");
            return;
        }

        String sql = """
                INSERT INTO linkedin_profile
                (search_result_id, first_name, last_name, role, linkedin_url, school_urn)
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                first_name = VALUES(first_name),
                last_name = VALUES(last_name),
                role = VALUES(role),
                school_urn = VALUES(school_urn)
                """;

        try {
            jdbcTemplate.update(sql,
                    profile.getSearchResultId(),
                    profile.getFirstName(),
                    profile.getLastName(),
                    profile.getRole(),
                    profile.getLinkedInUrl(),
                    profile.getSchoolUrn());
        } catch (Exception e) {
            logger.error("Failed to save profile for URL {}: {}", profile.getLinkedInUrl(), e.getMessage(), e);
        }
    }

    public String getSchoolUrnForTask(int taskId) {
        String sql = "SELECT urn FROM search_task WHERE id = ?";
        try {
            return jdbcTemplate.queryForObject(sql, String.class, taskId);
        } catch (Exception e) {
            logger.warn("Could not find school URN for task {}: {}", taskId, e.getMessage());
            return null;
        }
    }
}