package com.alpine.marketing.searchtask.repository;

import com.alpine.marketing.searchtask.dto.SearchQueryDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Repository for search query template operations.
 * Handles database interactions for the search_query table using JdbcTemplate.
 */
@Repository
public class SearchQueryRepository {

    private static final Logger logger = LoggerFactory.getLogger(SearchQueryRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public SearchQueryRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private static final RowMapper<SearchQueryDto> SEARCH_QUERY_ROW_MAPPER = new RowMapper<SearchQueryDto>() {
        @Override
        public SearchQueryDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return new SearchQueryDto(
                    rs.getInt("id"),
                    rs.getString("site"),
                    rs.getString("param"),
                    rs.getString("tag"),
                    rs.getInt("required_no_of_results"));
        }
    };

    public List<SearchQueryDto> findAll() {
        String sql = """
                SELECT id, site, param, tag, required_no_of_results
                FROM search_query
                ORDER BY priority ASC, id ASC
                """;

        try {
            List<SearchQueryDto> queries = jdbcTemplate.query(sql, SEARCH_QUERY_ROW_MAPPER);
            logger.info("Found {} total search query templates.", queries.size());
            return queries;
        } catch (Exception e) {
            logger.error("Failed to fetch all search queries: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch all search queries", e);
        }
    }

    public List<SearchQueryDto> findByTag(String tag) {
        String sql = """
                SELECT id, site, param, tag, required_no_of_results
                FROM search_query
                WHERE tag = ?
                ORDER BY priority ASC, id ASC
                """;

        try {
            List<SearchQueryDto> queries = jdbcTemplate.query(sql, SEARCH_QUERY_ROW_MAPPER, tag);
            logger.info("Found {} search query templates for tag '{}'", queries.size(), tag);
            return queries;
        } catch (Exception e) {
            logger.error("Failed to fetch search queries for tag '{}': {}", tag, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch search queries", e);
        }
    }

    public int countByTag(String tag) {
        String sql = "SELECT COUNT(*) FROM search_query WHERE tag = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tag);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Failed to count search queries for tag '{}': {}", tag, e.getMessage(), e);
            throw new RuntimeException("Failed to count search queries", e);
        }
    }
}