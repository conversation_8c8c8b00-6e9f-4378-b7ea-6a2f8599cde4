package com.alpine.marketing.searchtask.repository;

import com.alpine.marketing.searchtask.dto.SchoolDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Repository for school operations.
 * Handles database interactions for the school table using JdbcTemplate.
 */
@Repository
public class SchoolRepository {

    private static final Logger logger = LoggerFactory.getLogger(SchoolRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public SchoolRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private static final RowMapper<SchoolDto> SCHOOL_ROW_MAPPER = new RowMapper<SchoolDto>() {
        @Override
        public SchoolDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return new SchoolDto(
                    rs.getInt("urn"),
                    rs.getString("school"),
                    rs.getString("email_domain"),
                    rs.getString("web_domain"));
        }
    };

    public List<SchoolDto> findAllWithWebDomains() {
        String sql = """
                SELECT urn, school, email_domain, web_domain
                FROM school
                WHERE web_domain IS NOT NULL
                  AND web_domain != ''
                  AND TRIM(web_domain) != ''
                ORDER BY urn
                """;

        try {
            List<SchoolDto> schools = jdbcTemplate.query(sql, SCHOOL_ROW_MAPPER);
            logger.info("Found {} schools with valid web domains", schools.size());
            return schools;
        } catch (Exception e) {
            logger.error("Failed to fetch schools with web domains: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch schools", e);
        }
    }

    public int countSchoolsWithWebDomains() {
        String sql = """
                SELECT COUNT(*)
                FROM school
                WHERE web_domain IS NOT NULL
                  AND web_domain != ''
                  AND TRIM(web_domain) != ''
                """;

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Failed to count schools with web domains: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to count schools", e);
        }
    }

    public String getDomainByUrn(String schoolUrn) {
        String sql = "SELECT web_domain FROM school WHERE urn = ?";
        try {
            return jdbcTemplate.queryForObject(sql, String.class, schoolUrn);
        } catch (Exception e) {
            logger.warn("Domain not found for school URN {}: {}", schoolUrn, e.getMessage());
            return null;
        }
    }

    public String getEmailPattern(String schoolUrn) {
        String sql = "SELECT email_pattern FROM school WHERE urn = ?";
        try {
            return jdbcTemplate.queryForObject(sql, String.class, schoolUrn);
        } catch (Exception e) {
            logger.warn("Pattern not found for school URN {}: {}", schoolUrn, e.getMessage());
            return null;
        }
    }

    public void saveEmailPattern(String schoolUrn, String pattern) {
        String sql = "UPDATE school SET email_pattern = ? WHERE urn = ?";
        jdbcTemplate.update(sql, pattern, schoolUrn);
        logger.info("💾 Saved pattern '{}' for school {}", pattern, schoolUrn);
    }

    public int getPatternCount() {
        String sql = "SELECT COUNT(*) FROM school WHERE email_pattern IS NOT NULL";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        return count != null ? count : 0;
    }

    public List<Map<String, Object>> getTopPatterns() {
        String sql = """
                SELECT email_pattern, COUNT(*) as count
                FROM school
                WHERE email_pattern IS NOT NULL
                GROUP BY email_pattern
                ORDER BY count DESC
                LIMIT 5
                """;
        return jdbcTemplate.queryForList(sql);
    }

    public int getCompleteSchoolCount(int emailTarget) {
        String sql = """
                SELECT COUNT(DISTINCT school_urn)
                FROM (
                    SELECT school_urn, COUNT(id) as email_count
                    FROM linkedin_profile
                    WHERE email IS NOT NULL
                    GROUP BY school_urn
                ) as counts
                WHERE email_count >= ?
                """;
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, emailTarget);
        return count != null ? count : 0;
    }
}