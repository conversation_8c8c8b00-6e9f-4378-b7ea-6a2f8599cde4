package com.alpine.marketing.searchtask.repository;

import com.alpine.marketing.searchtask.dto.SearchTaskDto;
import com.alpine.marketing.searchtask.dto.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.ArrayList;

/**
 * Repository for search task operations.
 * Handles database interactions for the search_task table using JdbcTemplate.
 */
@Repository
public class SearchTaskRepository {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public SearchTaskRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private static final RowMapper<SearchTaskDto> SEARCH_TASK_ROW_MAPPER = (rs, rowNum) -> {
        SearchTaskDto task = new SearchTaskDto();
        task.setId(rs.getInt("id"));
        task.setSearchQueryId(rs.getInt("search_query_id"));
        task.setSchoolUrn(rs.getInt("urn"));
        task.setSearchSite(rs.getString("search_site"));
        task.setSearchParams(rs.getString("search_params"));
        task.setFinalQuery(rs.getString("final_query"));
        try {
            task.setStatus(TaskStatus.valueOf(rs.getString("status")));
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid status value '{}' found in database for task id {}", rs.getString("status"),
                    rs.getInt("id"));
            task.setStatus(TaskStatus.ERROR);
        }
        task.setErrorMessage(rs.getString("error_message"));
        task.setRequiredNoOfResults(rs.getInt("required_no_of_results"));

        long totalResults = rs.getLong("total_results");
        if (rs.wasNull()) {
            task.setTotalResults(null);
        } else {
            task.setTotalResults(totalResults);
        }
        return task;
    };

    public void createBatch(List<SearchTaskDto> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            logger.warn("No tasks to create");
            return;
        }

        String sql = """
                INSERT INTO search_task (search_query_id, urn, search_site, search_params, final_query, status)
                VALUES (?, ?, ?, ?, ?, ?)
                """;

        try {
            jdbcTemplate.batchUpdate(sql, tasks, tasks.size(),
                    (ps, task) -> {
                        ps.setInt(1, task.getSearchQueryId());
                        ps.setInt(2, task.getSchoolUrn());
                        ps.setString(3, task.getSearchSite());
                        ps.setString(4, task.getSearchParams());
                        ps.setString(5, task.getFinalQuery());
                        ps.setString(6, task.getStatus().name());
                    });

            logger.info("Successfully created {} search tasks in a batch operation.", tasks.size());
        } catch (Exception e) {
            logger.error("Failed to create search tasks batch: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create search tasks", e);
        }
    }

    public List<SearchTaskDto> findProcessableTasks(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT st.id, st.search_query_id, st.urn, st.search_site, st.search_params,
                       st.final_query, st.status, st.error_message, sq.required_no_of_results,
                       st.total_results
                FROM search_task st
                INNER JOIN search_query sq ON st.search_query_id = sq.id
                WHERE (st.status = ? OR st.status = ?) AND sq.tag = ?
                ORDER BY sq.priority ASC, st.id ASC
                """);

        List<Object> params = new ArrayList<>();
        params.add(TaskStatus.PENDING.name());
        params.add(TaskStatus.PARTIALLY_COMPLETED.name());
        params.add(tag);

        try {
            List<SearchTaskDto> tasks = jdbcTemplate.query(sql.toString(), SEARCH_TASK_ROW_MAPPER, params.toArray());
            logger.info("Found {} processable tasks for tag '{}'",
                    tasks.size(), tag);
            return tasks;
        } catch (Exception e) {
            logger.error("Failed to fetch processable tasks for tag '{}': {}", tag, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch processable tasks", e);
        }
    }

    public void updateTotalResults(int taskId, long totalResults) {
        String sql = "UPDATE search_task SET total_results = ? WHERE id = ?";
        try {
            jdbcTemplate.update(sql, totalResults, taskId);
            logger.info("Set total_results={} for task {}", totalResults, taskId);
        } catch (Exception e) {
            logger.error("Failed to update total_results for task {}: {}", taskId, e.getMessage());
        }
    }

    public void updateTaskCompletion(long taskId, TaskStatus status, String errorMessage) {
        String sql = """
                UPDATE search_task
                SET status = ?, error_message = ?, updated = CURRENT_TIMESTAMP
                WHERE id = ?
                """;

        try {
            int rowsUpdated = jdbcTemplate.update(sql, status.name(), errorMessage, taskId);
            if (rowsUpdated == 0) {
                logger.warn("No task found with ID {} to update completion", taskId);
            } else {
                logger.info("Set final status for task {}: status='{}'{}",
                        taskId, status,
                        errorMessage != null ? ", error: " + errorMessage : "");
            }
        } catch (Exception e) {
            logger.error("Failed to update task {} completion: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("Failed to update task completion", e);
        }
    }

    public int resetQuotaFailedTasks() {
        String sql = """
                UPDATE search_task
                SET status = ?, error_message = NULL, updated = CURRENT_TIMESTAMP
                WHERE status = ?
                """;

        try {
            int rowsUpdated = jdbcTemplate.update(sql, TaskStatus.PENDING.name(), TaskStatus.QUOTA_FAILED.name());
            logger.info("Reset {} quota-failed tasks back to PENDING status", rowsUpdated);
            return rowsUpdated;
        } catch (Exception e) {
            logger.error("Failed to reset quota-failed tasks: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to reset quota-failed tasks", e);
        }
    }

    public TaskStats getTaskStats() {
        String sql = """
                SELECT
                    COUNT(*) as total,
                    SUM(IF(status = 'PENDING', 1, 0)) as pending,
                    SUM(IF(status = 'COMPLETED', 1, 0)) as completed,
                    SUM(IF(status = 'QUOTA_FAILED', 1, 0)) as quota_failed,
                    SUM(IF(status = 'ERROR', 1, 0)) as error
                FROM search_task
                """;

        try {
            return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> new TaskStats(
                    rs.getInt("total"),
                    rs.getInt("pending"),
                    rs.getInt("completed"),
                    rs.getInt("quota_failed"),
                    rs.getInt("error")));
        } catch (Exception e) {
            logger.error("Failed to get task statistics: {}", e.getMessage(), e);
            return new TaskStats(0, 0, 0, 0, 0);
        }
    }

    public static class TaskStats {
        public final int total;
        public final int pending;
        public final int completed;
        public final int quotaFailed;
        public final int error;

        public TaskStats(int total, int pending, int completed, int quotaFailed, int error) {
            this.total = total;
            this.pending = pending;
            this.completed = completed;
            this.quotaFailed = quotaFailed;
            this.error = error;
        }

        @Override
        public String toString() {
            return String.format("TaskStats{total=%d, pending=%d, completed=%d, quotaFailed=%d, error=%d}",
                    total, pending, completed, quotaFailed, error);
        }
    }
}