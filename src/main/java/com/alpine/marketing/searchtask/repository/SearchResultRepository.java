package com.alpine.marketing.searchtask.repository;

import com.alpine.marketing.searchtask.dto.SearchResultDto;
import com.alpine.marketing.searchtask.dto.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for search result operations.
 * Handles database interactions for the search_result table using JdbcTemplate.
 */
@Repository
public class SearchResultRepository {

    private static final Logger logger = LoggerFactory.getLogger(SearchResultRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public SearchResultRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void saveBatch(List<SearchResultDto> results) {
        if (results == null || results.isEmpty()) {
            logger.info("No search results to save");
            return;
        }

        String sql = """
                INSERT INTO search_result (search_task_id, url, urn, title, snippet)
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE url = url
                """; // ON DUPLICATE KEY UPDATE does nothing but prevents errors on duplicate URLs

        try {
            jdbcTemplate.batchUpdate(sql, results, results.size(),
                    (ps, result) -> {
                        ps.setInt(1, result.getSearchTaskId());
                        ps.setString(2, result.getUrl());
                        ps.setInt(3, result.getSchoolUrn());
                        ps.setString(4, result.getTitle());
                        ps.setString(5, result.getSnippet());
                    });

            logger.info("Successfully saved or ignored {} search results in a batch operation.", results.size());
        } catch (Exception e) {
            logger.error("Failed to save search results batch: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to save search results", e);
        }
    }

    public List<SearchResultDto> findUnprocessedLinkedInResults(String tag) {
        String sql = """
                SELECT sr.id, sr.search_task_id, sr.url, sr.urn, sr.title, sr.snippet
                FROM search_result sr
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE st.status = ?
                  AND sr.url LIKE '%linkedin.com/in/%'
                  AND sr.scanned IS NULL
                  AND sq.tag = ?
                ORDER BY sq.priority ASC, sr.id ASC
                LIMIT 1000
                """;

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                SearchResultDto result = new SearchResultDto(
                        rs.getInt("search_task_id"),
                        rs.getString("url"),
                        rs.getInt("urn"),
                        rs.getString("title"),
                        rs.getString("snippet"));
                return result;
            }, TaskStatus.COMPLETED.name(), tag);
        } catch (Exception e) {
            logger.error("Failed to find unprocessed LinkedIn results for tag '{}': {}", tag, e.getMessage(), e);
            throw new RuntimeException("Failed to find unprocessed LinkedIn results", e);
        }
    }

    public Integer findIdByUrlAndTaskId(String url, int taskId) {
        String sql = "SELECT id FROM search_result WHERE url = ? AND search_task_id = ?";
        try {
            return jdbcTemplate.queryForObject(sql, Integer.class, url, taskId);
        } catch (Exception e) {
            logger.info("Could not find search result ID for URL '{}' in task {}: {}", url, taskId, e.getMessage());
            return null;
        }
    }

    public void markAsScanned(int searchResultId) {
        String sql = "UPDATE search_result SET scanned = CURRENT_TIMESTAMP WHERE id = ?";
        try {
            jdbcTemplate.update(sql, searchResultId);
        } catch (Exception e) {
            logger.error("Failed to mark search result ID {} as scanned: {}", searchResultId, e.getMessage(), e);
        }
    }

    public int countByTaskId(Integer taskId) {
        String sql = "SELECT COUNT(*) FROM search_result WHERE search_task_id = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, taskId);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Failed to count search results for task {}: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("Failed to count search results", e);
        }
    }

    public int deleteByTaskId(Integer taskId) {
        String sql = "DELETE FROM search_result WHERE search_task_id = ?";

        try {
            int deletedCount = jdbcTemplate.update(sql, taskId);
            if (deletedCount > 0) {
                logger.info("Deleted {} search results for task {}", deletedCount, taskId);
            }
            return deletedCount;
        } catch (Exception e) {
            logger.error("Failed to delete search results for task {}: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("Failed to delete search results", e);
        }
    }
}