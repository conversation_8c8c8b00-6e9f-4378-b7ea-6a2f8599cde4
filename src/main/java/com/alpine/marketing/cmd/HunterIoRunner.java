package com.alpine.marketing.cmd;

import com.alpine.marketing.hunterio.service.PatternInferenceService;
import com.alpine.marketing.hunterio.service.SeedEmailDiscoveryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.Arrays;

@Component
@Profile("hunterio")
public class HunterIoRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(HunterIoRunner.class);

    private final SeedEmailDiscoveryService seedEmailDiscoveryService;
    private final PatternInferenceService patternInferenceService;

    public HunterIoRunner(SeedEmailDiscoveryService seedEmailDiscoveryService,
            PatternInferenceService patternInferenceService) {
        this.seedEmailDiscoveryService = seedEmailDiscoveryService;
        this.patternInferenceService = patternInferenceService;
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("🚀 HunterIo Runner started with profile: hunterio");

        Map<String, String> argMap = parseArguments(args);
        String command = argMap.getOrDefault("command", "status");
        String tag = argMap.getOrDefault("tag", "blue");

        logger.info("Executing command: {}, tag: {}", command, tag);

        switch (command.toLowerCase()) {
            case "find-seed-emails" -> seedEmailDiscoveryService.executeFindSeedEmails(tag);
            case "infer-patterns" -> patternInferenceService.executeInferPatterns(tag);
            case "apply-patterns" -> patternInferenceService.executeApplyPatterns(tag);
            case "apply-verify" -> patternInferenceService.executeApplyVerify(tag);
            case "full-inference" -> {
                logger.info(
                        "Executing full inference workflow: find-seed-emails -> infer-patterns -> apply-patterns -> apply-verify");
                seedEmailDiscoveryService.executeFindSeedEmails(tag);
                patternInferenceService.executeInferPatterns(tag);
                patternInferenceService.executeApplyPatterns(tag);
                patternInferenceService.executeApplyVerify(tag);
            }
            case "status" -> showStatus();
            default -> logger.warn(
                    "Unknown command: {}. Use: find-seed-emails, infer-patterns, apply-patterns, apply-verify, full-inference, or status",
                    command);
        }
    }

    private Map<String, String> parseArguments(String[] args) {
        return Arrays.stream(args)
                .filter(arg -> arg.startsWith("--"))
                .map(arg -> arg.substring(2).split("=", 2))
                .filter(parts -> parts.length >= 1)
                .collect(Collectors.toMap(
                        parts -> parts[0],
                        parts -> parts.length == 2 ? parts[1] : "",
                        (existing, replacement) -> replacement));
    }

    private void showStatus() {
        logger.info("--- Hunter.io Status (Global) ---");
        seedEmailDiscoveryService.showStatus();
        logger.info("--------------------");
        patternInferenceService.showStatus();
        logger.info("--------------------");
    }
}