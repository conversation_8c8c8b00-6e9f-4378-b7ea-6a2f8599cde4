package com.alpine.marketing.cmd;

import com.alpine.marketing.searchtask.repository.SearchTaskRepository;
import com.alpine.marketing.searchtask.service.SearchTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Profile("search-tasks")
public class SearchTaskRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(SearchTaskRunner.class);

    private final SearchTaskService searchTaskService;

    public SearchTaskRunner(SearchTaskService searchTaskService) {
        this.searchTaskService = searchTaskService;
    }

    @Override
    public void run(String... args) {
        logger.info("🚀 Search Task Runner started with profile: search-tasks");

        Map<String, String> argMap = parseArguments(args);
        String command = argMap.getOrDefault("command", "status");
        String tag = argMap.getOrDefault("tag", "blue");

        logger.info("Executing command: {}, tag: {}", command, tag);

        switch (command.toLowerCase()) {
            case "create" -> executeCreateTasks();
            case "execute" -> executeSearchTasks(tag);
            case "process-linkedin" ->searchTaskService.executeProcessLinkedIn(tag);
            case "retry" -> retryFailedTasks();
            case "status" -> showStatus();
            default -> logger.warn(
                    "Unknown command: {}. Use: create, execute, process-linkedin, retry or status",
                    command);
        }
    }

    private Map<String, String> parseArguments(String[] args) {
        return Arrays.stream(args)
                .filter(arg -> arg.startsWith("--"))
                .map(arg -> arg.substring(2).split("=", 2))
                .filter(parts -> parts.length >= 1)
                .collect(Collectors.toMap(
                        parts -> parts[0],
                        parts -> parts.length == 2 ? parts[1] : "",
                        (existing, replacement) -> replacement));
    }

    private void executeCreateTasks() {
        logger.info("📋 Creating search tasks from all templates...");
        try {
            searchTaskService.createTasksFromTemplates();
            logger.info("✅ Task creation completed successfully.");
            showStatus();
        } catch (Exception e) {
            logger.error("❌ Task creation failed: {}", e.getMessage(), e);
            throw new RuntimeException("Task creation execution failed", e);
        }
    }

    private void executeSearchTasks(String tag) {
        logger.info("🔍 Executing pending search tasks for tag: '{}'",
                tag);

        try {
            searchTaskService.executePendingTasks(tag);
            logger.info("✅ Task execution completed for tag: '{}'", tag);

            showStatus();

        } catch (Exception e) {
            logger.error("❌ Task execution failed for tag '{}': {}", tag, e.getMessage(),
                    e);
            throw new RuntimeException("Task execution failed", e);
        }
    }

    private void showStatus() {
        logger.info("📊 Search Task Runner Status");

        try {
            SearchTaskRepository.TaskStats stats = searchTaskService.getTaskStats();

            logger.info("📋 Task Overview:");
            logger.info("  Total Tasks: {}", stats.total);
            logger.info("  Pending: {}", stats.pending);
            logger.info("  Completed: {}", stats.completed);
            logger.info("  Quota Failed: {}", stats.quotaFailed);
            logger.info("  Errors: {}", stats.error);

            double completionPercentage = stats.total > 0 ? (double) stats.completed / stats.total * 100 : 0.0;
            logger.info(String.format("  Completion Rate: %.1f%%", completionPercentage));

            if (stats.pending > 0) {
                logger.info("📝 Recommended Next Action:");
                logger.info("  Run 'execute --tag=<tag>' to process {} pending tasks",
                        stats.pending);
            } else if (stats.quotaFailed > 0) {
                logger.info("📝 Recommended Next Action:");
                logger.info("  Run 'retry' command to retry {} quota-failed tasks", stats.quotaFailed);
            } else if (stats.total == 0) {
                logger.info("📝 Recommended Next Action:");
                logger.info("  Run 'create' command to generate tasks from templates");
            } else {
                logger.info("✅ All tasks completed successfully!");
            }

        } catch (Exception e) {
            logger.error("❌ Status command failed: {}", e.getMessage(), e);
            throw new RuntimeException("Status execution failed", e);
        }
    }

    private void retryFailedTasks() {
        logger.info("🔄 Retrying quota-failed tasks");

        try {
            int resetCount = searchTaskService.resetQuotaFailedTasks();

            if (resetCount == 0) {
                logger.info("✅ No quota-failed tasks found to retry");
            } else {
                logger.info("✅ Reset {} tasks from QUOTA_FAILED to PENDING status", resetCount);
                logger.info("📝 Next Step: Run 'execute' command to process the reset tasks");
            }

            showStatus();

        } catch (Exception e) {
            logger.error("❌ Retry command failed: {}", e.getMessage(), e);
            throw new RuntimeException("Retry execution failed", e);
        }
    }
}