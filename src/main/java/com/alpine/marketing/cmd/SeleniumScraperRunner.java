package com.alpine.marketing.cmd;

import com.alpine.marketing.seleniumscraper.repository.ScraperRepository;
import com.alpine.marketing.seleniumscraper.service.SeleniumScraperService;
import com.alpine.marketing.seleniumscraper.service.WebDriverPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CommandLineRunner for executing Selenium scraping operations.
 * This runner is the entry point for the 'seleniumscraper' profile.
 * It integrates with the `searchtask` module by processing URLs from the
 * `search_result` table.
 */
@Component
@Profile("seleniumscraper")
public class SeleniumScraperRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(SeleniumScraperRunner.class);

    private final SeleniumScraperService seleniumScraperService;
    private final ScraperRepository scraperRepository;
    private final WebDriverPoolService webDriverPoolService;

    public SeleniumScraperRunner(SeleniumScraperService seleniumScraperService,
            ScraperRepository scraperRepository,
            WebDriverPoolService webDriverPoolService) {
        this.seleniumScraperService = seleniumScraperService;
        this.scraperRepository = scraperRepository;
        this.webDriverPoolService = webDriverPoolService;
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("🚀 Selenium Scraper Runner started with profile: seleniumscraper");


        Map<String, String> argMap = parseArguments(args);
        String command = argMap.getOrDefault("command", "status");
        String tag = argMap.getOrDefault("tag", null);

        logger.info("Executing command: {}, tag: {}", command, tag);

            switch (command.toLowerCase()) {
                case "scrape-emails" -> executeScrapeEmails(tag);
                case "status" -> showStatus(tag);
                default -> logger.warn("Unknown command: {}. Use: scrape-emails, or status", command);
            }
    }

    private Map<String, String> parseArguments(String[] args) {
        return Arrays.stream(args)
                .filter(arg -> arg.startsWith("--"))
                .map(arg -> arg.substring(2).split("=", 2))
                .filter(parts -> parts.length >= 1)
                .collect(Collectors.toMap(
                        parts -> parts[0],
                        parts -> parts.length == 2 ? parts[1] : "",
                        (existing, replacement) -> replacement));
    }

    private void executeScrapeEmails(String tag) {
        logger.info("🔍 Starting email scraping process...");
        showStatus(tag);

        SeleniumScraperService.ScrapingResult result = seleniumScraperService.executeEmailScraping(tag);
        logger.info("🎯 Scraping workflow completed: {}", result);

        logger.info("📊 Final Status after scraping:");
        showStatus(tag);

        if (result.hasError()) {
            throw new RuntimeException("Scraping completed with errors: " + result.getError());
        }
    }

    private void showStatus(String tag) {
        logger.info("📊 Selenium Scraper Status (Tag: {})",
                tag != null ? tag : "all");

        int totalUrls = scraperRepository.countTotalUrls(tag);
        int scannedUrls = scraperRepository.countScannedUrls(tag);
        int unscannedUrls = scraperRepository.countUnscannedUrls(tag);
        int scrapedEmails = scraperRepository.countScrapedEmails();

        double scannedPercentage = totalUrls > 0 ? ((double) scannedUrls / totalUrls) * 100.0 : 0.0;

        logger.info("  URL Statistics:");
        logger.info("    Total URLs (non-LinkedIn): {}", totalUrls);
        logger.info(String.format("    Scanned URLs: %d (%.1f%%)", scannedUrls, scannedPercentage));
        logger.info("    Unscanned URLs: {}", unscannedUrls);
        logger.info("    Total Valid Emails Found (Global): {}", scrapedEmails);

        WebDriverPoolService.PoolStats driverStats = webDriverPoolService.getPoolStats();
        logger.info("  WebDriver Pool:");
        logger.info("    Available Drivers: {}", driverStats.getAvailableDrivers());
        logger.info("    Drivers in Use: {}", driverStats.getInUse());
        logger.info("    Pool Status: {}", driverStats.isShutdown() ? "Shutdown" : "Active");

        if (unscannedUrls > 0) {
            logger.info("  ➡️ Next Action: Run 'scrape-emails' to process {} remaining URLs.", unscannedUrls);
        } else {
            logger.info("  ✅ All specified non-LinkedIn URLs have been processed!");
        }
    }
}