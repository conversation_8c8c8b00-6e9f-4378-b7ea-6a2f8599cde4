package com.alpine.marketing.hunterio.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "hunter")
public record HunterIoConfig(
        String apiKey,
        String emailFinderUrl,
        String emailVerifierUrl,
        long delayBetweenRequests,
        Processing processing) {

    public record Processing(
            int emailTargetPerSchool,
            int processingLimit,
            int minSeedEmails,
            double confidenceThreshold) {
    }

    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && !apiKey.equals("YOUR_HUNTER_API_KEY");
    }

    public String getConfigSummary() {
        return isConfigured() ? String.format("API Key: PRESENT, Delay: %dms", delayBetweenRequests)
                : "API Key is MISSING";
    }
}