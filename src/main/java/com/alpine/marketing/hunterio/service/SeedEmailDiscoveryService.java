package com.alpine.marketing.hunterio.service;

import com.alpine.marketing.hunterio.config.HunterIoConfig;
import com.alpine.marketing.hunterio.repository.HunterRepository;
import com.alpine.marketing.searchtask.repository.SchoolRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
public class SeedEmailDiscoveryService {
    private static final Logger logger = LoggerFactory.getLogger(SeedEmailDiscoveryService.class);

    private final HunterIoService hunterIoService;
    private final HunterRepository hunterRepository;
    private final SchoolRepository schoolRepository;
    private final HunterIoConfig hunterIoConfig;

    public SeedEmailDiscoveryService(
            HunterIoService hunterIoService,
            HunterRepository hunterRepository,
            SchoolRepository schoolRepository,
            HunterIoConfig hunterIoConfig) {
        this.hunterIoService = hunterIoService;
        this.hunterRepository = hunterRepository;
        this.schoolRepository = schoolRepository;
        this.hunterIoConfig = hunterIoConfig;
    }

    public void executeFindSeedEmails(String tag) {
        if (!hunterIoService.isConfigured()) {
            logger.warn("Hunter.io API key is not configured. Skipping email search.");
            return;
        }
        logger.info("🚀 Starting Phase 1: Seed Email Discovery...");

        List<String> schoolsToProcess = hunterRepository.getSchoolsToProcess(tag,
                hunterIoConfig.processing().emailTargetPerSchool(), hunterIoConfig.processing().processingLimit());

        if (schoolsToProcess.isEmpty()) {
            logger.info("✅ No schools require seed email processing at this time.");
            return;
        }

        logger.info("Found {} schools to process for seed emails.", schoolsToProcess.size());

        for (String schoolUrn : schoolsToProcess) {
            processSchool(schoolUrn, tag);
        }
        logger.info("✅ Finished Phase 1: Seed Email Discovery.");
    }

    private void processSchool(String schoolUrn, String tag) {
        String domain = schoolRepository.getDomainByUrn(schoolUrn);
        if (domain == null || domain.isBlank()) {
            logger.warn("Skipping school {} due to missing web domain.", schoolUrn);
            return;
        }

        int emailTarget = hunterIoConfig.processing().emailTargetPerSchool();
        List<Map<String, Object>> profiles = hunterRepository.getUnprocessedProfilesForSchool(schoolUrn, tag);

        logger.info("Processing {} profiles for school {} (Domain: {})", profiles.size(), schoolUrn, domain);

        int emailsFound = 0;
        for (Map<String, Object> profile : profiles) {
            if (emailsFound >= emailTarget) {
                logger.info("Target of {} emails reached for school {}. Moving to next school.", emailTarget,
                        schoolUrn);
                break;
            }

            if (processProfile(profile, domain)) {
                emailsFound++;
            }
        }
    }

    private boolean processProfile(Map<String, Object> profile, String domain) {
        int profileId = (int) profile.get("id");
        String firstName = (String) profile.get("first_name");
        String lastName = (String) profile.get("last_name");

        Optional<String> foundEmail = hunterIoService.findEmail(domain, firstName, lastName)
                .map(result -> result.email().toLowerCase());

        if (foundEmail.isPresent()) {
            hunterRepository.updateProfileWithEmail(profileId, foundEmail.get(), "hunter_finder");
            logger.info("✅ Found email for profile {}: {}", profileId, foundEmail.get());
            return true;
        } else {
            hunterRepository.markProfileAsProcessed(profileId);
            logger.info("❌ No email found for profile {}.", profileId);
            return false;
        }
    }

    public void showStatus() {
        if (!hunterIoService.isConfigured()) {
            logger.warn("Hunter API is not configured. Status may be incomplete.");
        }

        int totalSchools = hunterRepository.getTotalSchools();
        int emailTarget = hunterIoConfig.processing().emailTargetPerSchool();
        Integer complete = hunterRepository.getCompleteSchools(null, emailTarget);
        Integer pending = hunterRepository.getPendingSchools(null, emailTarget);
        Integer finderCount = hunterRepository.getFinderCount(null);

        logger.info("Seed Email Discovery Status (Global)");
        logger.info("Hunter API Status: {}", hunterIoService.getConfigSummary());
        logger.info("School Processing Status: {}/{} Total Schools are Complete ({} or more emails)",
                Objects.toString(complete, "N/A"), totalSchools, emailTarget);
        logger.info("School Processing Status: {} Schools Pending (less than {} emails)",
                Objects.toString(pending, "N/A"), emailTarget);
        logger.info("Finder Stats: {} emails found via Hunter Email Finder",
                Objects.toString(finderCount, "N/A"));
    }
}