package com.alpine.marketing.hunterio.service;

import com.alpine.marketing.hunterio.config.HunterIoConfig;
import com.alpine.marketing.hunterio.dto.EmailFinderResponseDto;
import com.alpine.marketing.hunterio.dto.EmailVerifierResponseDto;
import com.alpine.marketing.hunterio.dto.FindResultDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Service
public class HunterIoService {

    private static final Logger logger = LoggerFactory.getLogger(HunterIoService.class);

    private final HunterIoConfig config;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private CloseableHttpClient httpClient;
    private long lastRequestTime = 0;

    public HunterIoService(HunterIoConfig config) {
        this.config = config;
    }

    @PostConstruct
    public void initialize() {
        this.httpClient = HttpClientBuilder.create().build();
        logger.info("✅ Hunter.io Email Finder and Verifier initialized");
    }

    public Optional<FindResultDto> findEmail(String domain, String firstName, String lastName) {
        if (!isValidInput(domain, firstName, lastName)) {
            return Optional.empty();
        }

        try {
            rateLimit();
            String url = buildUrl(domain, firstName, lastName);
            logger.info("🔍 Searching: {} {} @{}", firstName, lastName, domain);

            return executeRequest(url)
                    .map(EmailFinderResponseDto::getData)
                    .filter(data -> data.getEmail() != null && !data.getEmail().isBlank())
                    .map(data -> FindResultDto.fromEmailFinder(data.getEmail()));

        } catch (Exception e) {
            logger.warn("API request failed: {}", e.getMessage());
            return Optional.empty();
        }
    }

    private boolean isValidInput(String domain, String firstName, String lastName) {
        return domain != null && !domain.trim().isEmpty() &&
                firstName != null && firstName.trim().length() > 1 &&
                lastName != null && lastName.trim().length() > 1;
    }

    private String buildUrl(String domain, String firstName, String lastName) {
        try {
            return String.format("%s?domain=%s&first_name=%s&last_name=%s&api_key=%s",
                    config.emailFinderUrl(),
                    URLEncoder.encode(domain, StandardCharsets.UTF_8),
                    URLEncoder.encode(firstName, StandardCharsets.UTF_8),
                    URLEncoder.encode(lastName, StandardCharsets.UTF_8),
                    URLEncoder.encode(config.apiKey(), StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException("Failed to build URL", e);
        }
    }

    private Optional<EmailFinderResponseDto> executeRequest(String url) {
        try {
            HttpGet request = new HttpGet(url);
            request.setHeader("Accept", "application/json");

            return httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                String body = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

                if (statusCode != 200) {
                    logger.warn("HTTP {}: {}", statusCode, body);
                    return Optional.<EmailFinderResponseDto>empty();
                }

                if (body == null || body.isBlank()) {
                    logger.warn("Empty response from Hunter.io");
                    return Optional.<EmailFinderResponseDto>empty();
                }

                try {
                    return Optional.of(objectMapper.readValue(body, EmailFinderResponseDto.class));
                } catch (Exception e) {
                    logger.warn("JSON parsing failed: {}", e.getMessage());
                    return Optional.<EmailFinderResponseDto>empty();
                }
            });

        } catch (Exception e) {
            logger.warn("Request execution failed: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<EmailVerifierResponseDto> verifyEmail(String email) {
        if (!isValidEmailInput(email)) {
            return Optional.empty();
        }

        try {
            rateLimit();
            String url = buildVerifierUrl(email);
            logger.info("🔍 Verifying email: {}", email);

            return executeVerifierRequest(url);

        } catch (Exception e) {
            logger.warn("Email verification failed: {}", e.getMessage());
            return Optional.empty();
        }
    }

    private boolean isValidEmailInput(String email) {
        return email != null && !email.trim().isEmpty() && email.contains("@");
    }

    private String buildVerifierUrl(String email) {
        try {
            return String.format("%s?email=%s&api_key=%s",
                    config.emailVerifierUrl(),
                    URLEncoder.encode(email, StandardCharsets.UTF_8),
                    URLEncoder.encode(config.apiKey(), StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException("Failed to build verifier URL", e);
        }
    }

    private Optional<EmailVerifierResponseDto> executeVerifierRequest(String url) {
        try {
            HttpGet request = new HttpGet(url);
            request.setHeader("Accept", "application/json");

            return httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                String body = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

                if (statusCode != 200) {
                    logger.warn("HTTP {}: {}", statusCode, body);
                    return Optional.<EmailVerifierResponseDto>empty();
                }

                if (body == null || body.isBlank()) {
                    logger.warn("Empty response from Hunter.io verifier");
                    return Optional.<EmailVerifierResponseDto>empty();
                }

                try {
                    return Optional.of(objectMapper.readValue(body, EmailVerifierResponseDto.class));
                } catch (Exception e) {
                    logger.warn("JSON parsing failed for verifier: {}", e.getMessage());
                    return Optional.<EmailVerifierResponseDto>empty();
                }
            });

        } catch (Exception e) {
            logger.warn("Verifier request execution failed: {}", e.getMessage());
            return Optional.empty();
        }
    }

    private void rateLimit() {
        long now = System.currentTimeMillis();
        long elapsed = now - lastRequestTime;

        if (elapsed < config.delayBetweenRequests()) {
            try {
                long sleep = config.delayBetweenRequests() - elapsed;
                logger.info("Rate limiting: sleeping {}ms", sleep);
                Thread.sleep(sleep);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Rate limiting interrupted", e);
            }
        }

        lastRequestTime = System.currentTimeMillis();
    }

    public boolean isConfigured() {
        return config.isConfigured();
    }

    public String getConfigSummary() {
        return config.getConfigSummary();
    }

    @PreDestroy
    public void destroy() {
        if (httpClient != null) {
            try {
                httpClient.close();
                logger.info("HTTP client closed");
            } catch (Exception e) {
                logger.warn("Error closing HTTP client: {}", e.getMessage());
            }
        }
    }
}