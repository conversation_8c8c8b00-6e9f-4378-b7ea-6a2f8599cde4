package com.alpine.marketing.hunterio.service;

import com.alpine.marketing.hunterio.config.HunterIoConfig;
import com.alpine.marketing.hunterio.dto.EmailVerifierResponseDto;
import com.alpine.marketing.hunterio.repository.HunterRepository;
import com.alpine.marketing.hunterio.repository.PatternInferenceRepository;
import com.alpine.marketing.searchtask.repository.SchoolRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ArrayList;

@Service
public class PatternInferenceService {

    private static final Logger logger = LoggerFactory.getLogger(PatternInferenceService.class);

    private final PatternInferenceRepository patternInferenceRepository;
    private final HunterRepository hunterRepository;
    private final SchoolRepository schoolRepository;
    private final EmailPatternService emailPatternService;
    private final HunterIoService hunterIoService;
    private final HunterIoConfig hunterIoConfig;

    public PatternInferenceService(
            PatternInferenceRepository patternInferenceRepository,
            HunterRepository hunterRepository,
            SchoolRepository schoolRepository,
            EmailPatternService emailPatternService,
            HunterIoService hunterIoService,
            HunterIoConfig hunterIoConfig) {
        this.patternInferenceRepository = patternInferenceRepository;
        this.hunterRepository = hunterRepository;
        this.schoolRepository = schoolRepository;
        this.emailPatternService = emailPatternService;
        this.hunterIoService = hunterIoService;
        this.hunterIoConfig = hunterIoConfig;
    }

    public void executeInferPatterns(String tag) {
        logger.info("🚀 Starting Phase 2a: Inferring Email Patterns...");
        int minSeedEmails = hunterIoConfig.processing().minSeedEmails();
        int processingLimit = hunterIoConfig.processing().processingLimit();
        List<String> schools = patternInferenceRepository.getSchoolsForPatternInference(tag, minSeedEmails,
                processingLimit);

        if (schools.isEmpty()) {
            logger.info("✅ No schools have enough seed emails (≥ {}) to infer a pattern.", minSeedEmails);
            return;
        }

        logger.info("Found {} schools with enough seed data to infer patterns.", schools.size());

        int patternsFound = 0;
        for (String schoolUrn : schools) {
            if (inferAndSavePatternForSchool(schoolUrn)) {
                patternsFound++;
            }
        }
        logger.info("✅ Finished pattern inference. Found and saved {} new patterns.", patternsFound);
    }

    private boolean inferAndSavePatternForSchool(String schoolUrn) {
        List<Map<String, Object>> seedEmails = patternInferenceRepository.getSeedEmailsForSchool(schoolUrn);

        if (seedEmails.size() < hunterIoConfig.processing().minSeedEmails()) {
            logger.warn("School {} has only {} seed emails (minimum: {}). Cannot infer pattern.",
                    schoolUrn, seedEmails.size(), hunterIoConfig.processing().minSeedEmails());
            return false;
        }

        String bestPattern = emailPatternService.inferPattern(seedEmails);
        if (bestPattern == null) {
            logger.warn("Could not determine a pattern for school {}", schoolUrn);
            return false;
        }

        schoolRepository.saveEmailPattern(schoolUrn, bestPattern);
        logger.info("Inferred and saved pattern '{}' for school {}", bestPattern, schoolUrn);
        return true;
    }

    public void executeApplyPatterns(String tag) {
        logger.info("🚀 Starting Phase 2b: Applying Inferred Email Patterns...");
        List<String> schoolsToProcess = patternInferenceRepository.getSchoolsForPatternApplication(tag);

        if (schoolsToProcess.isEmpty()) {
            logger.info("✅ No schools with inferred patterns need processing.");
            return;
        }

        logger.info("Found {} schools with patterns to apply.", schoolsToProcess.size());

        int profilesUpdated = 0;
        for (String schoolUrn : schoolsToProcess) {
            profilesUpdated += applyPatternToSchool(schoolUrn, tag);
        }
        logger.info("✅ Finished applying patterns. Updated {} profiles.", profilesUpdated);
    }

    private int applyPatternToSchool(String schoolUrn, String tag) {
        String pattern = schoolRepository.getEmailPattern(schoolUrn);
        String domain = schoolRepository.getDomainByUrn(schoolUrn);

        if (pattern == null || domain == null || domain.isBlank()) {
            logger.warn("Missing pattern or domain for school {}, cannot apply patterns.", schoolUrn);
            return 0;
        }

        List<Map<String, Object>> profiles = patternInferenceRepository.getProfilesForPatternApplication(schoolUrn,
                tag);
        if (profiles.isEmpty()) {
            return 0;
        }

        List<Map<String, Object>> emailUpdates = new ArrayList<>();
        for (Map<String, Object> profile : profiles) {
            int profileId = (int) profile.get("id");
            String firstName = (String) profile.get("first_name");
            String lastName = (String) profile.get("last_name");

            String email = emailPatternService.generateEmail(pattern, firstName, lastName, domain);
            if (email != null) {
                emailUpdates.add(Map.of(
                        "profileId", profileId,
                        "email", email));
            }
        }

        if (!emailUpdates.isEmpty()) {
            patternInferenceRepository.batchUpdateProfilesWithEmails(emailUpdates);
            logger.info("Applied pattern '{}' to {} profiles for school {}", pattern, emailUpdates.size(), schoolUrn);
        }

        return emailUpdates.size();
    }

    public void executeApplyVerify(String tag) {
        if (!hunterIoService.isConfigured()) {
            logger.warn("Hunter.io API key is not configured. Skipping email verification.");
            return;
        }

        logger.info("🚀 Starting Phase 3: Email Verification for Inferred Patterns...");
        int processingLimit = hunterIoConfig.processing().processingLimit();
        List<Map<String, Object>> profilesToVerify = hunterRepository.getProfilesForVerification(tag, processingLimit);

        if (profilesToVerify.isEmpty()) {
            logger.info("✅ No emails with inferred patterns need verification at this time.");
            return;
        }

        logger.info("Found {} emails to verify.", profilesToVerify.size());

        int verifiedCount = 0;
        List<Map<String, Object>> batchUpdates = new ArrayList<>();

        for (Map<String, Object> profile : profilesToVerify) {
            int profileId = (int) profile.get("id");
            String email = (String) profile.get("email");

            if (verifyProfileEmail(profileId, email, batchUpdates)) {
                verifiedCount++;
            }

            if (batchUpdates.size() >= 50) {
                hunterRepository.batchUpdateVerificationData(batchUpdates);
                batchUpdates.clear();
                logger.info("Processed batch of 50 verifications...");
            }
        }

        if (!batchUpdates.isEmpty()) {
            hunterRepository.batchUpdateVerificationData(batchUpdates);
        }

        logger.info("✅ Finished email verification. Verified {} emails.", verifiedCount);
    }

    private boolean verifyProfileEmail(int profileId, String email, List<Map<String, Object>> batchUpdates) {
        try {
            var verificationResult = hunterIoService.verifyEmail(email);

            if (verificationResult.isPresent()) {
                EmailVerifierResponseDto.Data data = verificationResult.get().getData();

                if (data != null) {
                    batchUpdates.add(Map.of(
                            "profileId", profileId,
                            "status", data.getStatus() != null ? data.getStatus() : "unknown",
                            "result", data.getResult() != null ? data.getResult() : "unknown",
                            "score", data.getScore() != null ? data.getScore() : 0,
                            "acceptAll", data.getAcceptAll() != null ? data.getAcceptAll() : false,
                            "block", data.getBlock() != null ? data.getBlock() : false));

                    logger.info("✅ Verified {}: {} (score: {})", email, data.getStatus(), data.getScore());
                    return true;
                }
            }

            hunterRepository.markProfileAsVerificationFailed(profileId);
            logger.info("❌ Verification failed for {}", email);
            return false;

        } catch (Exception e) {
            logger.warn("Error verifying email {}: {}", email, e.getMessage());
            hunterRepository.markProfileAsVerificationFailed(profileId);
            return false;
        }
    }

    public void showStatus() {
        int totalSchools = schoolRepository.countSchoolsWithWebDomains();
        int patternsInferred = schoolRepository.getPatternCount();
        int emailsApplied = patternInferenceRepository.getInferredEmailCount(null);
        long profilesToProcess = patternInferenceRepository.countProfilesForPatternApplication(null,
                hunterIoConfig.processing().confidenceThreshold());
        int complete = schoolRepository.getCompleteSchoolCount(hunterIoConfig.processing().emailTargetPerSchool());

        Integer verifiedCount = hunterRepository.getVerificationCount(null);
        Integer validEmailCount = hunterRepository.getVerifiedValidCount(null);
        Integer pendingVerification = hunterRepository.getPendingVerificationCount(null);

        logger.info("Pattern Inference Status (Global)");
        logger.info("Configuration:");
        logger.info("  - Min Seed Emails for Inference: {}", hunterIoConfig.processing().minSeedEmails());
        logger.info("  - Confidence Threshold for Apply: {}", hunterIoConfig.processing().confidenceThreshold());
        logger.info("  - Email Target for Completion: {}", hunterIoConfig.processing().emailTargetPerSchool());
        logger.info("Inference Status:");
        logger.info("  - {}/{} schools have an inferred email pattern.", patternsInferred, totalSchools);
        logger.info("Application Status:");
        logger.info("  - {} emails generated from applied patterns.", emailsApplied);
        logger.info("  - {} profiles could be updated if 'apply-patterns' is run.", profilesToProcess);
        logger.info("Verification Status:");
        logger.info("  - {} emails have been verified.", Objects.toString(verifiedCount, "N/A"));
        logger.info("  - {} emails verified as valid.", Objects.toString(validEmailCount, "N/A"));
        logger.info("  - {} emails pending verification.", Objects.toString(pendingVerification, "N/A"));
        logger.info("Completion Status:");
        logger.info("  - {}/{} schools are considered complete.",
                Objects.toString(complete, "N/A"), totalSchools);
    }
}