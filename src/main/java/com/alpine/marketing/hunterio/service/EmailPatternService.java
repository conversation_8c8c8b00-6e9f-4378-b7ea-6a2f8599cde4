package com.alpine.marketing.hunterio.service;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class EmailPatternService {

    public String inferPattern(List<Map<String, Object>> seedEmails) {
        if (seedEmails.isEmpty()) {
            return null;
        }

        Map<String, Integer> patternCounts = new java.util.HashMap<>();

        for (Map<String, Object> emailData : seedEmails) {
            String email = (String) emailData.get("email");
            String firstName = (String) emailData.get("first_name");
            String lastName = (String) emailData.get("last_name");

            if (email == null || firstName == null || lastName == null) {
                continue;
            }

            String pattern = detectPattern(email, firstName, lastName);
            if (pattern != null) {
                patternCounts.merge(pattern, 1, Integer::sum);
            }
        }

        return patternCounts.isEmpty() ? null
                : patternCounts.entrySet().stream()
                        .max(Map.Entry.comparingByValue())
                        .get().getKey();
    }

    public String generateEmail(String pattern, String firstName, String lastName, String domain) {
        if (firstName == null || lastName == null || domain == null || pattern == null) {
            return null;
        }

        String f = cleanName(firstName).toLowerCase();
        String l = cleanName(lastName).toLowerCase();

        if (f.isEmpty() || l.isEmpty()) {
            return null;
        }

        String fi = String.valueOf(f.charAt(0));

        String localPart = switch (pattern) {
            case "{first}.{last}" -> f + "." + l;
            case "{f}.{last}" -> fi + "." + l;
            case "{first}{last}" -> f + l;
            case "{f}{last}" -> fi + l;
            case "{last}.{first}" -> l + "." + f;
            case "{last}{first}" -> l + f;
            default -> null;
        };

        return localPart != null ? localPart + "@" + domain : null;
    }

    private String detectPattern(String email, String firstName, String lastName) {
        if (email == null || firstName == null || lastName == null) {
            return null;
        }

        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return null;
        }

        String localPart = email.substring(0, atIndex).toLowerCase();
        String f = cleanName(firstName).toLowerCase();
        String l = cleanName(lastName).toLowerCase();

        if (f.isEmpty() || l.isEmpty()) {
            return null;
        }

        String fi = String.valueOf(f.charAt(0));

        if (localPart.equals(f + "." + l)) {
            return "{first}.{last}";
        }
        if (localPart.equals(fi + "." + l)) {
            return "{f}.{last}";
        }
        if (localPart.equals(f + l)) {
            return "{first}{last}";
        }
        if (localPart.equals(fi + l)) {
            return "{f}{last}";
        }
        if (localPart.equals(l + "." + f)) {
            return "{last}.{first}";
        }
        if (localPart.equals(l + f)) {
            return "{last}{first}";
        }

        return null;
    }

    private String cleanName(String name) {
        if (name == null) {
            return "";
        }
        return name.trim().replaceAll("[^a-zA-Z]", "");
    }
}