package com.alpine.marketing.hunterio.repository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class PatternInferenceRepository {

    private static final Logger logger = LoggerFactory.getLogger(PatternInferenceRepository.class);
    private final JdbcTemplate jdbcTemplate;

    public PatternInferenceRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<String> getSchoolsForPatternInference(String tag, int minSeedEmails, int processingLimit) {
        StringBuilder sql = new StringBuilder("""
                SELECT lp.school_urn
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                JOIN school s ON lp.school_urn = s.urn
                WHERE s.web_domain IS NOT NULL AND s.web_domain != ''
                AND s.email_pattern IS NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" GROUP BY lp.school_urn");
        sql.append(" HAVING COUNT(CASE WHEN lp.email IS NOT NULL AND lp.method IN ('hunter_finder') THEN 1 END) >= ?");
        params.add(minSeedEmails);

        sql.append(" ORDER BY MIN(sq.priority) ASC, lp.school_urn LIMIT ?");
        params.add(processingLimit);

        return jdbcTemplate.queryForList(sql.toString(), String.class, params.toArray());
    }

    public List<String> getSchoolsForPatternApplication(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT s.urn
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                JOIN school s ON lp.school_urn = s.urn
                WHERE s.web_domain IS NOT NULL AND s.web_domain != ''
                AND s.email_pattern IS NOT NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" AND EXISTS (")
                .append("SELECT 1 FROM linkedin_profile p2 ")
                .append("WHERE p2.school_urn = s.urn ")
                .append("AND p2.email IS NULL ")
                .append("AND p2.first_name IS NOT NULL AND p2.first_name != '' ")
                .append("AND p2.last_name IS NOT NULL AND p2.last_name != ''")
                .append(")")
                .append(" GROUP BY s.urn")
                .append(" ORDER BY MIN(sq.priority) ASC, s.urn");

        return jdbcTemplate.queryForList(sql.toString(), String.class, params.toArray());
    }

    public List<Map<String, Object>> getSeedEmailsForSchool(String schoolUrn) {
        String sql = """
                SELECT email, first_name, last_name
                FROM linkedin_profile
                WHERE school_urn = ?
                AND email IS NOT NULL
                AND method IN ('hunter_finder')
                AND first_name IS NOT NULL AND first_name != ''
                AND last_name IS NOT NULL AND last_name != ''
                ORDER BY hunter_processed_at ASC
                """;
        return jdbcTemplate.queryForList(sql, schoolUrn);
    }

    public List<Map<String, Object>> getProfilesForPatternApplication(String schoolUrn, String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT lp.id, lp.first_name, lp.last_name
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.school_urn = ?
                AND lp.email IS NULL
                AND lp.first_name IS NOT NULL AND lp.first_name != ''
                AND lp.last_name IS NOT NULL AND lp.last_name != ''
                """);

        List<Object> params = new ArrayList<>();
        params.add(schoolUrn);
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" GROUP BY lp.id, lp.first_name, lp.last_name, lp.found_at");
        sql.append(" ORDER BY MIN(sq.priority) ASC, lp.found_at ASC");
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    public void updateProfileWithGeneratedEmail(int profileId, String email) {
        String sql = """
                UPDATE linkedin_profile
                SET email = ?, method = ?, hunter_processed_at = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, email, "inferred_pattern", Timestamp.from(Instant.now()), profileId);
    }

    public void markProfileAsProcessedWithoutEmail(int profileId) {
        String sql = """
                UPDATE linkedin_profile
                SET method = ?, hunter_processed_at = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, "pattern_failed", Timestamp.from(Instant.now()), profileId);
    }

    public void batchUpdateProfilesWithEmails(List<Map<String, Object>> updates) {
        String sql = """
                UPDATE linkedin_profile
                SET email = ?, method = ?, hunter_processed_at = ?
                WHERE id = ?
                """;

        List<Object[]> batchArgs = updates.stream()
                .map(update -> new Object[] {
                        update.get("email"),
                        "inferred_pattern",
                        Timestamp.from(Instant.now()),
                        update.get("profileId")
                })
                .toList();

        jdbcTemplate.batchUpdate(sql, batchArgs);
        logger.info("Batch updated {} profiles with generated emails", updates.size());
    }

    public Integer getReadyForInferenceCount(String tag, int minSeedEmails) {
        StringBuilder subQuery = new StringBuilder("""
                SELECT COUNT(DISTINCT lp.school_urn)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                JOIN school s ON lp.school_urn = s.urn
                WHERE s.web_domain IS NOT NULL AND s.web_domain != ''
                AND s.email_pattern IS NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            subQuery.append(" AND sq.tag = ?");
            params.add(tag);
        }

        String sql = subQuery
                + " GROUP BY lp.school_urn HAVING COUNT(CASE WHEN lp.email IS NOT NULL AND lp.method IN ('hunter_finder') THEN 1 END) >= ?";
        params.add(minSeedEmails);

        Integer result = jdbcTemplate.query(
                "SELECT COUNT(*) FROM (" + sql + ") as sub",
                rs -> rs.next() ? rs.getInt(1) : 0,
                params.toArray());

        return result != null ? result : 0;
    }

    public Integer getReadyForApplicationCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(DISTINCT s.urn)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                JOIN school s ON lp.school_urn = s.urn
                WHERE s.web_domain IS NOT NULL AND s.web_domain != ''
                AND s.email_pattern IS NOT NULL
                AND EXISTS (SELECT 1 FROM linkedin_profile p2 WHERE p2.school_urn = s.urn AND p2.email IS NULL)
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }

    public Integer getPatternGeneratedEmailsCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(lp.id)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.method = 'inferred_pattern'
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }

    public int getInferredEmailCount(String tag) {
        String sql = "SELECT COUNT(*) FROM linkedin_profile WHERE method = 'inferred_pattern'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        return count != null ? count : 0;
    }

    public long countProfilesForPatternApplication(String tag, double confidenceThreshold) {
        String sql = "SELECT COUNT(*) FROM linkedin_profile WHERE email IS NULL AND school_urn IN (SELECT urn FROM school WHERE email_pattern IS NOT NULL)";
        Long count = jdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count : 0;
    }
}