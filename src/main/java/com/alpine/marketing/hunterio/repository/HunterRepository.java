package com.alpine.marketing.hunterio.repository;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.sql.Timestamp;
import java.time.Instant;

@Repository
public class HunterRepository {

    private final JdbcTemplate jdbcTemplate;

    public HunterRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<String> getSchoolsToProcess(String tag, int emailTarget, int processingLimit) {
        StringBuilder sql = new StringBuilder("""
                SELECT lp.school_urn
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                JOIN school s ON lp.school_urn = s.urn
                WHERE s.web_domain IS NOT NULL AND s.web_domain != ''
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" GROUP BY lp.school_urn");
        sql.append(" HAVING COUNT(CASE WHEN lp.email IS NOT NULL THEN 1 END) < ?");
        params.add(emailTarget);

        sql.append(" ORDER BY MIN(sq.priority) ASC, lp.school_urn LIMIT ?");
        params.add(processingLimit);

        return jdbcTemplate.queryForList(sql.toString(), String.class, params.toArray());
    }

    public List<Map<String, Object>> getUnprocessedProfilesForSchool(String schoolUrn, String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT lp.id, lp.first_name, lp.last_name
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.school_urn = ?
                AND lp.hunter_processed_at IS NULL
                AND lp.first_name IS NOT NULL AND lp.first_name != ''
                AND lp.last_name IS NOT NULL AND lp.last_name != ''
                """);

        List<Object> params = new ArrayList<>();
        params.add(schoolUrn);
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" GROUP BY lp.id, lp.first_name, lp.last_name, lp.found_at");
        sql.append(" ORDER BY MIN(sq.priority) ASC, lp.found_at ASC LIMIT 20");
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    public void updateProfileWithEmail(int profileId, String email, String method) {
        String sql = """
                UPDATE linkedin_profile
                SET email = ?, method = ?, hunter_processed_at = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, email, method, Timestamp.from(Instant.now()), profileId);
    }

    public void markProfileAsProcessed(int profileId) {
        String sql = """
                UPDATE linkedin_profile
                SET hunter_processed_at = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, Timestamp.from(Instant.now()), profileId);
    }

    public Integer getTotalSchools() {
        String sql = "SELECT COUNT(DISTINCT school_urn) FROM linkedin_profile";
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

    public Integer getCompleteSchools(String tag, int emailTarget) {
        return getSchoolCountByEmailThreshold(tag, emailTarget, ">=");
    }

    public Integer getPendingSchools(String tag, int emailTarget) {
        return getSchoolCountByEmailThreshold(tag, emailTarget, "<");
    }

    private Integer getSchoolCountByEmailThreshold(String tag, int emailTarget, String operator) {
        StringBuilder filteredSchoolsSubquery = new StringBuilder("""
                SELECT DISTINCT lp.school_urn
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE 1=1
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            filteredSchoolsSubquery.append(" AND sq.tag = ?");
            params.add(tag);
        }

        String sql = String.format("""
                SELECT COUNT(DISTINCT p1.school_urn)
                FROM linkedin_profile p1
                WHERE p1.school_urn IN (%s) AND (
                    SELECT COUNT(*)
                    FROM linkedin_profile p2
                    WHERE p2.school_urn = p1.school_urn AND p2.email IS NOT NULL
                ) %s ?
                """, filteredSchoolsSubquery.toString(), operator);

        params.add(emailTarget);
        return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
    }

    public Integer getFinderCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(lp.id)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.method = 'hunter_finder'
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }

    public List<Map<String, Object>> getProfilesForVerification(String tag, int limit) {
        StringBuilder sql = new StringBuilder("""
                SELECT lp.id, lp.email
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.method = 'inferred_pattern'
                AND lp.email IS NOT NULL AND lp.email != ''
                AND lp.hunter_verify_at IS NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        sql.append(" GROUP BY lp.id, lp.email, lp.found_at");
        sql.append(" ORDER BY MIN(sq.priority) ASC, lp.found_at ASC LIMIT ?");
        params.add(limit);

        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    public void updateProfileWithVerificationData(int profileId, String status, String result, Integer score,
            Boolean acceptAll, Boolean block) {
        String sql = """
                UPDATE linkedin_profile
                SET hunter_verify_at = ?, status = ?, result = ?, score = ?, accept_all = ?, block = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, Timestamp.from(Instant.now()), status, result, score, acceptAll, block, profileId);
    }

    public void markProfileAsVerificationFailed(int profileId) {
        String sql = """
                UPDATE linkedin_profile
                SET hunter_verify_at = ?, status = ?
                WHERE id = ?
                """;
        jdbcTemplate.update(sql, Timestamp.from(Instant.now()), "verification_failed", profileId);
    }

    public void batchUpdateVerificationData(List<Map<String, Object>> updates) {
        String sql = """
                UPDATE linkedin_profile
                SET hunter_verify_at = ?, status = ?, result = ?, score = ?, accept_all = ?, block = ?
                WHERE id = ?
                """;

        List<Object[]> batchArgs = updates.stream()
                .map(update -> new Object[] {
                        Timestamp.from(Instant.now()),
                        update.get("status"),
                        update.get("result"),
                        update.get("score"),
                        update.get("acceptAll"),
                        update.get("block"),
                        update.get("profileId")
                })
                .toList();

        jdbcTemplate.batchUpdate(sql, batchArgs);
    }

    public Integer getVerificationCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(lp.id)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.hunter_verify_at IS NOT NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }

    public Integer getVerifiedValidCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(lp.id)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.status = 'valid'
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }

    public Integer getPendingVerificationCount(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(lp.id)
                FROM linkedin_profile lp
                JOIN search_result sr ON lp.search_result_id = sr.id
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE lp.method = 'inferred_pattern'
                AND lp.email IS NOT NULL AND lp.email != ''
                AND lp.hunter_verify_at IS NULL
                """);

        List<Object> params = new ArrayList<>();
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }
}