package com.alpine.marketing.hunterio.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class BulkVerificationRequestDto {

    @JsonProperty("emails")
    private List<String> emails;

    public BulkVerificationRequestDto() {
    }

    public BulkVerificationRequestDto(List<String> emails) {
        this.emails = emails;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }
}