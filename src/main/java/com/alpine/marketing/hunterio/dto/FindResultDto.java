package com.alpine.marketing.hunterio.dto;

public record FindResultDto(String email, String method) {

    public static FindResultDto fromEmailFinder(String email) {
        return new FindResultDto(email, "hunter_finder");
    }

    public static FindResultDto fromPattern(String email) {
        return new FindResultDto(email, "inferred_pattern");
    }

    public boolean isFromApi() {
        return "hunter_finder".equals(method);
    }

    public boolean isFromPattern() {
        return "inferred_pattern".equals(method);
    }
}