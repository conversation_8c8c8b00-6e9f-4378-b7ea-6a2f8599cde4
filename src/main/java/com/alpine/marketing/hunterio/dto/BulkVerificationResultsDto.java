package com.alpine.marketing.hunterio.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BulkVerificationResultsDto {

    @JsonProperty("results")
    private List<VerificationResult> results;

    public BulkVerificationResultsDto() {
    }

    public List<VerificationResult> getResults() {
        return results;
    }

    public void setResults(List<VerificationResult> results) {
        this.results = results;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VerificationResult {
        @JsonProperty("email")
        private String email;

        @JsonProperty("status")
        private String status;

        @JsonProperty("result")
        private String result;

        @JsonProperty("score")
        private Integer score;

        @JsonProperty("regexp")
        private Boolean regexp;

        @JsonProperty("gibberish")
        private Boolean gibberish;

        @JsonProperty("disposable")
        private Boolean disposable;

        @JsonProperty("webmail")
        private Boolean webmail;

        @JsonProperty("mx_records")
        private Boolean mxRecords;

        @JsonProperty("smtp_server")
        private Boolean smtpServer;

        @JsonProperty("smtp_check")
        private Boolean smtpCheck;

        @JsonProperty("accept_all")
        private Boolean acceptAll;

        @JsonProperty("block")
        private Boolean block;

        public VerificationResult() {
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }

        public Boolean getRegexp() {
            return regexp;
        }

        public void setRegexp(Boolean regexp) {
            this.regexp = regexp;
        }

        public Boolean getGibberish() {
            return gibberish;
        }

        public void setGibberish(Boolean gibberish) {
            this.gibberish = gibberish;
        }

        public Boolean getDisposable() {
            return disposable;
        }

        public void setDisposable(Boolean disposable) {
            this.disposable = disposable;
        }

        public Boolean getWebmail() {
            return webmail;
        }

        public void setWebmail(Boolean webmail) {
            this.webmail = webmail;
        }

        public Boolean getMxRecords() {
            return mxRecords;
        }

        public void setMxRecords(Boolean mxRecords) {
            this.mxRecords = mxRecords;
        }

        public Boolean getSmtpServer() {
            return smtpServer;
        }

        public void setSmtpServer(Boolean smtpServer) {
            this.smtpServer = smtpServer;
        }

        public Boolean getSmtpCheck() {
            return smtpCheck;
        }

        public void setSmtpCheck(Boolean smtpCheck) {
            this.smtpCheck = smtpCheck;
        }

        public Boolean getAcceptAll() {
            return acceptAll;
        }

        public void setAcceptAll(Boolean acceptAll) {
            this.acceptAll = acceptAll;
        }

        public Boolean getBlock() {
            return block;
        }

        public void setBlock(Boolean block) {
            this.block = block;
        }
    }
}