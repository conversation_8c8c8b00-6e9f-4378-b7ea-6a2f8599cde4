package com.alpine.marketing.seleniumscraper.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * Selenium configuration properties for Spring Boot.
 * Provides type-safe configuration binding for Selenium WebDriver settings.
 */
@Component
@ConfigurationProperties(prefix = "selenium")
public class SeleniumProperties {

    private Duration pageLoadTimeout = Duration.ofSeconds(30);
    private Duration implicitWaitTimeout = Duration.ofSeconds(10);
    private boolean headless = true;
    private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

    private Pool pool = new Pool();
    private Performance performance = new Performance();
    private Document document = new Document();
    private DriverManagement driverManagement = new DriverManagement();
    private Humanization humanization = new Humanization();

    public static class Pool {
        private int maxSize = 3;
        private int initialSize = 1;

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        public int getInitialSize() {
            return initialSize;
        }

        public void setInitialSize(int initialSize) {
            this.initialSize = initialSize;
        }
    }

    public static class Performance {
        private boolean enableImages = false;
        private boolean enableJavaScript = false;
        private boolean enableExtensions = false;
        private boolean enablePlugins = false;
        private int memorySize = 4096;

        public boolean isEnableImages() {
            return enableImages;
        }

        public void setEnableImages(boolean enableImages) {
            this.enableImages = enableImages;
        }

        public boolean isEnableJavaScript() {
            return enableJavaScript;
        }

        public void setEnableJavaScript(boolean enableJavaScript) {
            this.enableJavaScript = enableJavaScript;
        }

        public boolean isEnableExtensions() {
            return enableExtensions;
        }

        public void setEnableExtensions(boolean enableExtensions) {
            this.enableExtensions = enableExtensions;
        }

        public boolean isEnablePlugins() {
            return enablePlugins;
        }

        public void setEnablePlugins(boolean enablePlugins) {
            this.enablePlugins = enablePlugins;
        }

        public int getMemorySize() {
            return memorySize;
        }

        public void setMemorySize(int memorySize) {
            this.memorySize = memorySize;
        }
    }

    public static class Document {
        private long maxSizeMb = 10;
        private Duration connectionTimeout = Duration.ofSeconds(30);
        private Duration readTimeout = Duration.ofSeconds(60);

        public long getMaxSizeMb() {
            return maxSizeMb;
        }

        public void setMaxSizeMb(long maxSizeMb) {
            this.maxSizeMb = maxSizeMb;
        }

        public Duration getConnectionTimeout() {
            return connectionTimeout;
        }

        public void setConnectionTimeout(Duration connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
        }

        public Duration getReadTimeout() {
            return readTimeout;
        }

        public void setReadTimeout(Duration readTimeout) {
            this.readTimeout = readTimeout;
        }

        public long getMaxSizeBytes() {
            return maxSizeMb * 1024 * 1024;
        }
    }

    public static class DriverManagement {
        private boolean autoDownload = true;
        private boolean useSystemProperty = false;
        private Duration downloadTimeout = Duration.ofSeconds(120);
        private Duration apiTimeout = Duration.ofSeconds(30);
        private boolean enableVersionCheck = true;
        private boolean enableCache = true;
        private int maxRetries = 3;

        public boolean isAutoDownload() {
            return autoDownload;
        }

        public void setAutoDownload(boolean autoDownload) {
            this.autoDownload = autoDownload;
        }

        public boolean isUseSystemProperty() {
            return useSystemProperty;
        }

        public void setUseSystemProperty(boolean useSystemProperty) {
            this.useSystemProperty = useSystemProperty;
        }

        public Duration getDownloadTimeout() {
            return downloadTimeout;
        }

        public void setDownloadTimeout(Duration downloadTimeout) {
            this.downloadTimeout = downloadTimeout;
        }

        public Duration getApiTimeout() {
            return apiTimeout;
        }

        public void setApiTimeout(Duration apiTimeout) {
            this.apiTimeout = apiTimeout;
        }

        public boolean isEnableVersionCheck() {
            return enableVersionCheck;
        }

        public void setEnableVersionCheck(boolean enableVersionCheck) {
            this.enableVersionCheck = enableVersionCheck;
        }

        public boolean isEnableCache() {
            return enableCache;
        }

        public void setEnableCache(boolean enableCache) {
            this.enableCache = enableCache;
        }

        public int getMaxRetries() {
            return maxRetries;
        }

        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }
    }

    public static class Humanization {
        private boolean enabled = false;
        private DelayRange pageLoadDelay = new DelayRange(Duration.ofSeconds(2), Duration.ofSeconds(5));
        private DelayRange actionDelay = new DelayRange(Duration.ofMillis(500), Duration.ofMillis(1500));
        private DelayRange typingDelay = new DelayRange(Duration.ofMillis(100), Duration.ofMillis(300));
        private ScrollBehavior scrollBehavior = new ScrollBehavior();
        private MouseBehavior mouseBehavior = new MouseBehavior();

        public static class DelayRange {
            private Duration min;
            private Duration max;

            public DelayRange() {
                this.min = Duration.ofMillis(100);
                this.max = Duration.ofMillis(500);
            }

            public DelayRange(Duration min, Duration max) {
                this.min = min;
                this.max = max;
            }

            public Duration getMin() {
                return min;
            }

            public void setMin(Duration min) {
                this.min = min;
            }

            public Duration getMax() {
                return max;
            }

            public void setMax(Duration max) {
                this.max = max;
            }
        }

        public static class ScrollBehavior {
            private boolean enabled = true;
            private DelayRange delay = new DelayRange(Duration.ofMillis(800), Duration.ofSeconds(2));
            private int maxScrolls = 3;
            private double scrollPercentage = 0.3; // Scroll 30% of viewport height

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public DelayRange getDelay() {
                return delay;
            }

            public void setDelay(DelayRange delay) {
                this.delay = delay;
            }

            public int getMaxScrolls() {
                return maxScrolls;
            }

            public void setMaxScrolls(int maxScrolls) {
                this.maxScrolls = maxScrolls;
            }

            public double getScrollPercentage() {
                return scrollPercentage;
            }

            public void setScrollPercentage(double scrollPercentage) {
                this.scrollPercentage = scrollPercentage;
            }
        }

        public static class MouseBehavior {
            private boolean enabled = false;
            private DelayRange moveDelay = new DelayRange(Duration.ofMillis(200), Duration.ofMillis(800));

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public DelayRange getMoveDelay() {
                return moveDelay;
            }

            public void setMoveDelay(DelayRange moveDelay) {
                this.moveDelay = moveDelay;
            }
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public DelayRange getPageLoadDelay() {
            return pageLoadDelay;
        }

        public void setPageLoadDelay(DelayRange pageLoadDelay) {
            this.pageLoadDelay = pageLoadDelay;
        }

        public DelayRange getActionDelay() {
            return actionDelay;
        }

        public void setActionDelay(DelayRange actionDelay) {
            this.actionDelay = actionDelay;
        }

        public DelayRange getTypingDelay() {
            return typingDelay;
        }

        public void setTypingDelay(DelayRange typingDelay) {
            this.typingDelay = typingDelay;
        }

        public ScrollBehavior getScrollBehavior() {
            return scrollBehavior;
        }

        public void setScrollBehavior(ScrollBehavior scrollBehavior) {
            this.scrollBehavior = scrollBehavior;
        }

        public MouseBehavior getMouseBehavior() {
            return mouseBehavior;
        }

        public void setMouseBehavior(MouseBehavior mouseBehavior) {
            this.mouseBehavior = mouseBehavior;
        }
    }

    // Main getters and setters
    public Duration getPageLoadTimeout() {
        return pageLoadTimeout;
    }

    public void setPageLoadTimeout(Duration pageLoadTimeout) {
        this.pageLoadTimeout = pageLoadTimeout;
    }

    public Duration getImplicitWaitTimeout() {
        return implicitWaitTimeout;
    }

    public void setImplicitWaitTimeout(Duration implicitWaitTimeout) {
        this.implicitWaitTimeout = implicitWaitTimeout;
    }

    public boolean isHeadless() {
        return headless;
    }

    public void setHeadless(boolean headless) {
        this.headless = headless;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public Pool getPool() {
        return pool;
    }

    public void setPool(Pool pool) {
        this.pool = pool;
    }

    public Performance getPerformance() {
        return performance;
    }

    public void setPerformance(Performance performance) {
        this.performance = performance;
    }

    public Document getDocument() {
        return document;
    }

    public void setDocument(Document document) {
        this.document = document;
    }

    public DriverManagement getDriverManagement() {
        return driverManagement;
    }

    public void setDriverManagement(DriverManagement driverManagement) {
        this.driverManagement = driverManagement;
    }

    public Humanization getHumanization() {
        return humanization;
    }

    public void setHumanization(Humanization humanization) {
        this.humanization = humanization;
    }
}