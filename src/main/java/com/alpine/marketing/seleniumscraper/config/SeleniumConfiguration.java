package com.alpine.marketing.seleniumscraper.config;

import com.alpine.marketing.seleniumscraper.service.WebDriverFactory;
import com.alpine.marketing.seleniumscraper.service.WebDriverPoolService;
import com.alpine.marketing.seleniumscraper.util.PlatformDetector;
import com.alpine.marketing.seleniumscraper.util.WebDriverDownloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Selenium WebDriver components.
 * Provides conditional bean creation based on platform support and
 * configuration.
 */
@Configuration
@EnableConfigurationProperties(SeleniumProperties.class)
public class SeleniumConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(SeleniumConfiguration.class);

    @Bean
    @ConditionalOnProperty(name = "selenium.enabled", havingValue = "true", matchIfMissing = true)
    public WebDriverDownloader webDriverDownloader() {
        return new WebDriverDownloader();
    }

    @Bean
    @ConditionalOnProperty(name = "selenium.enabled", havingValue = "true", matchIfMissing = true)
    public WebDriverFactory webDriverFactory(SeleniumProperties properties, WebDriverDownloader webDriverDownloader) {
        // Check platform compatibility before creating factory
        PlatformDetector.Architecture arch = PlatformDetector.detectArchitecture();
        if (!arch.isSupported()) {
            logger.warn("WebDriver platform not fully supported: {}. " +
                    "WebDriverFactory will attempt to use system ChromeDriver.",
                    PlatformDetector.getPlatformInfo());
        }

        return new WebDriverFactory(properties, webDriverDownloader);
    }

    @Bean
    @ConditionalOnProperty(name = "selenium.enabled", havingValue = "true", matchIfMissing = true)
    public WebDriverPoolService webDriverPoolService(SeleniumProperties properties, WebDriverFactory webDriverFactory) {
        // Always create the pool service - it will handle Chrome unavailability
        // gracefully
        WebDriverPoolService poolService = new WebDriverPoolService(properties, webDriverFactory);
        logger.info("✅ WebDriver pool service initialized (drivers may be limited if Chrome unavailable)");
        return poolService;
    }

    /**
     * Fallback configuration when WebDriver is disabled or unavailable.
     * This allows the application to start even when WebDriver cannot be
     * initialized.
     */
    @Configuration
    @ConditionalOnProperty(name = "selenium.enabled", havingValue = "false")
    static class DisabledSeleniumConfiguration {

        private static final Logger logger = LoggerFactory.getLogger(DisabledSeleniumConfiguration.class);

        public DisabledSeleniumConfiguration() {
            logger.info("🚫 Selenium WebDriver is disabled via configuration. " +
                    "Web scraping functionality will not be available.");
        }
    }
}
