package com.alpine.marketing.seleniumscraper.dto;

/**
 * A unified Data Transfer Object that holds all the necessary information
 * for processing a single URL scraping task.
 */
public class ScrapingTaskDto {
    private final int searchResultId;
    private final int urn;
    private final String url;
    private final String emailDomain;

    public ScrapingTaskDto(int searchResultId, int urn, String url, String emailDomain) {
        this.searchResultId = searchResultId;
        this.urn = urn;
        this.url = url;
        this.emailDomain = emailDomain;
    }

    public int getSearchResultId() {
        return searchResultId;
    }

    public int getUrn() {
        return urn;
    }

    public String getUrl() {
        return url;
    }

    public String getEmailDomain() {
        return emailDomain;
    }
}