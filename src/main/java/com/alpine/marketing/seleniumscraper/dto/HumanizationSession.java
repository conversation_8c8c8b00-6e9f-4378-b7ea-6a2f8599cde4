package com.alpine.marketing.seleniumscraper.dto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * A stateful object to manage the "momentum" of a single humanization session.
 * This removes the need for static state and allows for cleaner, more
 * predictable behavior.
 */
public class HumanizationSession {
    private static final Logger logger = LoggerFactory.getLogger(HumanizationSession.class);
    private static final Random random = new Random();

    private double sessionMomentum = 1.0;
    private int actionCount = 0;

    /**
     * Records that an action has been taken and updates the session's momentum.
     * Simulates a user getting faster as they become more familiar with a site.
     */
    public void recordAction() {
        actionCount++;

        // Gradually reduce delays up to 30% as user gets familiar (realistic range)
        if (actionCount <= 20) {
            sessionMomentum = Math.max(0.7, 1.0 - (actionCount * 0.015));
        }

        // Occasional fatigue spikes (reset momentum slightly)
        if (actionCount > 0 && actionCount % 25 == 0 && random.nextDouble() < 0.2) {
            sessionMomentum = Math.min(1.0, sessionMomentum + 0.1);
            logger.info("Session fatigue: momentum reset to {}", String.format("%.2f", sessionMomentum));
        }
    }

    public double getSessionMomentum() {
        return sessionMomentum;
    }

    public String getStats() {
        return String.format("Actions: %d, Momentum: %.2f", actionCount, sessionMomentum);
    }
}