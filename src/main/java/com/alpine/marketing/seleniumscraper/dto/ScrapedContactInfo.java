package com.alpine.marketing.seleniumscraper.dto;

import java.util.Objects;

/**
 * Data Transfer Object for raw contact information scraped from a website.
 * This has been simplified to align with the new `search_result` architecture.
 */
public class ScrapedContactInfo {

    private final String email;
    private final String sourceUrl;
    private final int searchResultId;
    private final int urn;

    public ScrapedContactInfo(String email, String sourceUrl, int searchResultId, int urn) {
        this.email = email;
        this.sourceUrl = sourceUrl;
        this.searchResultId = searchResultId;
        this.urn = urn;
    }

    public String getEmail() {
        return email;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public int getSearchResultId() {
        return searchResultId;
    }

    public int getUrn() {
        return urn;
    }

    @Override
    public String toString() {
        return "ScrapedContactInfo{" +
                "email='" + email + '\'' +
                ", sourceUrl='" + sourceUrl + '\'' +
                ", searchResultId=" + searchResultId +
                ", urn=" + urn +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ScrapedContactInfo that = (ScrapedContactInfo) o;
        return searchResultId == that.searchResultId &&
                urn == that.urn &&
                Objects.equals(email, that.email) &&
                Objects.equals(sourceUrl, that.sourceUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email, sourceUrl, searchResultId, urn);
    }
}