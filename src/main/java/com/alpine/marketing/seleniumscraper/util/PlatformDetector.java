package com.alpine.marketing.seleniumscraper.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class to detect the current operating system, architecture,
 * and local Chrome browser version.
 */
public final class PlatformDetector {

    private static final Logger logger = LoggerFactory.getLogger(PlatformDetector.class);
    private static final Pattern VERSION_PATTERN = Pattern.compile("(\\d+\\.\\d+\\.\\d+\\.\\d+)");

    private PlatformDetector() {
        // Private constructor to prevent instantiation
    }

    public static Architecture detectArchitecture() {
        String osName = System.getProperty("os.name").toLowerCase();
        String osArch = System.getProperty("os.arch").toLowerCase();
        if (osName.contains("mac") && osArch.contains("aarch64"))
            return Architecture.ARM64;
        if (osName.contains("linux") && osArch.contains("amd64"))
            return Architecture.LINUX64;
        return Architecture.UNSUPPORTED;
    }

    public static String detectChromeVersion() {
        try {
            String osName = System.getProperty("os.name").toLowerCase();
            ProcessBuilder pb;
            if (osName.contains("mac")) {
                pb = new ProcessBuilder("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version");
            } else if (osName.contains("linux")) {
                pb = new ProcessBuilder("google-chrome", "--version");
            } else {
                return null; // Unsupported OS for detection
            }
            Process process = pb.start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null) {
                    Matcher matcher = VERSION_PATTERN.matcher(line);
                    if (matcher.find()) {
                        String version = matcher.group(1);
                        logger.info("Detected Chrome version: {}", version);
                        return version;
                    }
                }
            }
        } catch (IOException e) {
            logger.warn(
                    "Could not detect Chrome version. It may not be installed or not in the system's PATH. Error: {}",
                    e.getMessage());
        }
        return null;
    }

    public static String getMajorVersion(String fullVersion) {
        return fullVersion != null && !fullVersion.isEmpty() ? fullVersion.split("\\.")[0] : null;
    }

    public static String getDriverBinaryName() {
        return System.getProperty("os.name").toLowerCase().contains("windows") ? "chromedriver.exe" : "chromedriver";
    }

    public static String getPlatformInfo() {
        return String.format("Platform: %s %s (%s)", System.getProperty("os.name"), System.getProperty("os.version"),
                System.getProperty("os.arch"));
    }

    public enum Architecture {
        ARM64("mac-arm64", "arm64"),
        LINUX64("linux64", "linux64"),
        UNSUPPORTED("unsupported", "unsupported");

        private final String chromeTestingPlatform;
        private final String folderName;

        Architecture(String chromeTestingPlatform, String folderName) {
            this.chromeTestingPlatform = chromeTestingPlatform;
            this.folderName = folderName;
        }

        public String getChromeTestingPlatform() {
            return chromeTestingPlatform;
        }

        public String getFolderName() {
            return folderName;
        }
    }
}