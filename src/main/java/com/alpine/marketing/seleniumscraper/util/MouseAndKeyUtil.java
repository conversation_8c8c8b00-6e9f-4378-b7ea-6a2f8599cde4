package com.alpine.marketing.seleniumscraper.util;

import com.alpine.marketing.seleniumscraper.config.SeleniumProperties;
import com.alpine.marketing.seleniumscraper.dto.HumanizationSession;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Random;

/**
 * Utility class for simulating human-like mouse and keyboard interactions in
 * Selenium.
 */
public final class MouseAndKeyUtil {

    private static final Logger logger = LoggerFactory.getLogger(MouseAndKeyUtil.class);
    private static final Random random = new Random();

    private MouseAndKeyUtil() {
    }

    /**
     * Simulates realistic human typing with bursts, pauses, and natural rhythm
     * variations.
     */
    public static void simulateTyping(WebElement element, String text,
            SeleniumProperties.Humanization.DelayRange typingDelay) {
        if (element == null || text == null || text.isEmpty()) {
            logger.warn("Invalid parameters for simulateTyping");
            return;
        }

        logger.info("Simulating realistic human typing for text length: {}", text.length());
        element.clear();
        TimingUtil.sleep(200 + random.nextInt(400)); // Pause after clearing

        int pos = 0;
        while (pos < text.length()) {
            int burstLength = Math.min(3 + random.nextInt(5), text.length() - pos);
            String burst = text.substring(pos, pos + burstLength);

            for (char c : burst.toCharArray()) {
                element.sendKeys(String.valueOf(c));
                long intraBurstDelay = typingDelay.getMin().toMillis() / 2;
                intraBurstDelay += random.nextInt((int) (intraBurstDelay));
                TimingUtil.sleep(intraBurstDelay);
            }

            pos += burstLength;

            if (pos < text.length()) {
                long pauseDelay = typingDelay.getMin().toMillis() +
                        random.nextInt((int) (typingDelay.getMax().toMillis() - typingDelay.getMin().toMillis()));
                TimingUtil.sleep(pauseDelay);
            }
        }
        logger.info("Completed realistic typing simulation");
    }

    /**
     * Simulates realistic mouse movement, including curved paths and pauses.
     */
    public static void simulateMouseMovement(Actions actions,
            SeleniumProperties.Humanization.MouseBehavior mouseBehavior, HumanizationSession session) {
        try {
            int waypoints = 2 + random.nextInt(4);
            for (int i = 0; i < waypoints; i++) {
                int deltaX = random.nextInt(80) - 40;
                int deltaY = random.nextInt(60) - 30;

                actions.moveByOffset(deltaX, deltaY).perform();
                TimingUtil.randomDelay(mouseBehavior.getMoveDelay().getMin(), mouseBehavior.getMoveDelay().getMax(),
                        session);
            }
            logger.info("Completed mouse movement simulation");
        } catch (Exception e) {
            logger.info("Mouse movement simulation failed (normal in headless mode): {}", e.getMessage());
        }
    }
}