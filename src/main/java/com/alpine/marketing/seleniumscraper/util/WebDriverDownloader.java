package com.alpine.marketing.seleniumscraper.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * A focused utility for finding, downloading, and caching the correct
 * ChromeDriver executable from the Google Chrome for Testing APIs.
 */
@Component
public class WebDriverDownloader {

    private static final Logger logger = LoggerFactory.getLogger(WebDriverDownloader.class);
    private static final String VERSIONS_API_URL = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json";
    private static final String LATEST_API_URL = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions.json";

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ConcurrentHashMap<String, String> driverPathCache = new ConcurrentHashMap<>();

    public String getDriverPath(String chromeVersion) {
        PlatformDetector.Architecture arch = PlatformDetector.detectArchitecture();
        if (arch == PlatformDetector.Architecture.UNSUPPORTED) {
            throw new RuntimeException(
                    "Unsupported platform for automatic ChromeDriver management: "
                            + PlatformDetector.getPlatformInfo());
        }

        if (chromeVersion == null) {
            logger.warn("Unable to detect Chrome version, attempting to use latest stable ChromeDriver.");
            return getLatestDriverPath(arch);
        }

        String cacheKey = arch.name() + "_" + chromeVersion;
        if (driverPathCache.containsKey(cacheKey) && Files.exists(Paths.get(driverPathCache.get(cacheKey)))) {
            logger.info("Using cached ChromeDriver from memory: {}", driverPathCache.get(cacheKey));
            return driverPathCache.get(cacheKey);
        }

        try {
            ChromeDriverInfo driverInfo = findCompatibleDriver(chromeVersion, arch);
            if (driverInfo == null) {
                logger.warn("No compatible ChromeDriver found for Chrome {}, trying latest.", chromeVersion);
                return getLatestDriverPath(arch);
            }
            String driverPath = downloadAndCacheDriver(driverInfo, arch);
            driverPathCache.put(cacheKey, driverPath);
            logger.info("✅ ChromeDriver ready: {} (version: {})", driverPath, driverInfo.version);
            return driverPath;
        } catch (Exception e) {
            throw new RuntimeException("ChromeDriver management failed for version " + chromeVersion, e);
        }
    }

    private String getLatestDriverPath(PlatformDetector.Architecture arch) {
        try {
            ChromeDriverInfo latestDriver = getLatestDriverInfo(arch);
            String driverPath = downloadAndCacheDriver(latestDriver, arch);
            driverPathCache.put(arch.name() + "_latest", driverPath);
            logger.info("✅ Latest ChromeDriver ready: {} (version: {})", driverPath, latestDriver.version);
            return driverPath;
        } catch (Exception e) {
            throw new RuntimeException("Latest ChromeDriver download failed", e);
        }
    }

    private ChromeDriverInfo findCompatibleDriver(String chromeVersion, PlatformDetector.Architecture arch)
            throws IOException {
        String majorVersion = PlatformDetector.getMajorVersion(chromeVersion);
        JsonNode versions = objectMapper.readTree(downloadApiResponse(VERSIONS_API_URL)).get("versions");

        for (JsonNode versionNode : versions) {
            String fullVersion = versionNode.get("version").asText();
            if (PlatformDetector.getMajorVersion(fullVersion).equals(majorVersion)) {
                return extractDriverInfo(versionNode, arch);
            }
        }
        return null;
    }

    private ChromeDriverInfo getLatestDriverInfo(PlatformDetector.Architecture arch) throws IOException {
        JsonNode channels = objectMapper.readTree(downloadApiResponse(LATEST_API_URL)).get("channels");
        JsonNode stableChannel = channels.get("Stable");
        if (stableChannel != null) {
            return extractDriverInfoFromDownloads(stableChannel.get("downloads").get("chromedriver"),
                    stableChannel.get("version").asText(), arch);
        }
        return null;
    }

    private ChromeDriverInfo extractDriverInfo(JsonNode versionNode, PlatformDetector.Architecture arch) {
        return extractDriverInfoFromDownloads(versionNode.get("downloads").get("chromedriver"),
                versionNode.get("version").asText(), arch);
    }

    private ChromeDriverInfo extractDriverInfoFromDownloads(JsonNode chromedriverNode, String version,
            PlatformDetector.Architecture arch) {
        for (JsonNode downloadNode : chromedriverNode) {
            if (arch.getChromeTestingPlatform().equals(downloadNode.get("platform").asText())) {
                return new ChromeDriverInfo(version, downloadNode.get("url").asText());
            }
        }
        return null;
    }

    private String downloadAndCacheDriver(ChromeDriverInfo driverInfo, PlatformDetector.Architecture arch)
            throws IOException {
        String driverBinaryName = PlatformDetector.getDriverBinaryName();
        Path driverDir = Paths.get("src/main/resources/chromedriver/", arch.getFolderName(), driverInfo.version);
        Path driverPath = driverDir.resolve(driverBinaryName);

        if (Files.exists(driverPath)) {
            logger.info("ChromeDriver already cached on disk: {}", driverPath);
            return driverPath.toString();
        }

        logger.info("📥 Downloading ChromeDriver {} for {}", driverInfo.version, arch);
        Files.createDirectories(driverDir);

        byte[] zipData = downloadFile(driverInfo.downloadUrl);
        extractChromeDriver(zipData, driverDir, driverBinaryName);

        if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
            Files.setPosixFilePermissions(driverPath,
                    java.nio.file.attribute.PosixFilePermissions.fromString("rwxr-xr-x"));
        }
        return driverPath.toString();
    }

    private void extractChromeDriver(byte[] zipData, Path targetDir, String driverBinaryName) throws IOException {
        try (ZipInputStream zipStream = new ZipInputStream(new ByteArrayInputStream(zipData))) {
            ZipEntry entry;
            while ((entry = zipStream.getNextEntry()) != null) {
                if (entry.getName().endsWith(driverBinaryName)) {
                    Path outputPath = targetDir.resolve(driverBinaryName);
                    Files.copy(zipStream, outputPath, StandardCopyOption.REPLACE_EXISTING);
                    logger.info("Extracted ChromeDriver to {}", outputPath);
                    return;
                }
            }
        }
        throw new IOException("ChromeDriver executable not found in ZIP archive.");
    }

    private byte[] downloadFile(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection con = (HttpURLConnection) url.openConnection();
        con.setRequestMethod("GET");
        try (InputStream in = con.getInputStream(); ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            in.transferTo(out);
            return out.toByteArray();
        }
    }

    private String downloadApiResponse(String urlString) throws IOException {
        return new String(downloadFile(urlString));
    }

    private static class ChromeDriverInfo {
        final String version;
        final String downloadUrl;

        ChromeDriverInfo(String version, String downloadUrl) {
            this.version = version;
            this.downloadUrl = downloadUrl;
        }
    }
}