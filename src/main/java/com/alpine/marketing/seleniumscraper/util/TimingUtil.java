package com.alpine.marketing.seleniumscraper.util;

import com.alpine.marketing.seleniumscraper.config.SeleniumProperties;
import com.alpine.marketing.seleniumscraper.dto.HumanizationSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Random;

/**
 * Utility class for generating human-like delays and timing patterns for
 * Selenium.
 */
public final class TimingUtil {

    private static final Logger logger = LoggerFactory.getLogger(TimingUtil.class);
    private static final Random random = new Random();

    private TimingUtil() {
    }

    /**
     * Pauses execution for a human-like duration within the given range,
     * accounting for session momentum.
     */
    public static void randomDelay(Duration min, Duration max, HumanizationSession session) {
        if (min == null || max == null || session == null) {
            logger.warn("Null parameter provided to randomDelay, skipping");
            return;
        }

        long minMillis = min.toMillis();
        long maxMillis = max.toMillis();

        if (minMillis > maxMillis) {
            long temp = minMillis;
            minMillis = maxMillis;
            maxMillis = temp;
        }

        if (minMillis == maxMillis) {
            sleep(minMillis);
            return;
        }

        long humanDelay = generateHumanTiming(minMillis, maxMillis, session.getSessionMomentum());

        logger.info("Applying human-like delay: {}ms (range: {}-{}ms, momentum: {})",
                humanDelay, minMillis, maxMillis, String.format("%.2f", session.getSessionMomentum()));
        sleep(humanDelay);
        session.recordAction();
    }

    /**
     * Generates a realistic timing value using a log-normal distribution, adjusted
     * for session momentum.
     */
    private static long generateHumanTiming(long minMillis, long maxMillis, double sessionMomentum) {
        double range = maxMillis - minMillis;
        double clusterPoint = 0.3; // Humans tend to be faster than the middle of a range.

        double beta = generateBetaDistribution(2.0, 5.0); // Skewed toward faster times.
        long baseDelay = minMillis + (long) (range * (clusterPoint + beta * (1 - clusterPoint)));

        baseDelay = (long) (baseDelay * sessionMomentum);
        baseDelay = (long) (baseDelay * (0.9 + (random.nextDouble() * 0.2))); // Micro-variations

        if (random.nextDouble() < 0.05) { // Occasional "think time" spikes
            baseDelay += (long) (range * 0.3 * random.nextDouble());
        }

        return Math.max(minMillis, Math.min(maxMillis, baseDelay));
    }

    /**
     * Generates a value from a beta distribution to create more realistic, skewed
     * timing.
     */
    private static double generateBetaDistribution(double alpha, double beta) {
        double u1 = random.nextDouble();
        double u2 = random.nextDouble();
        double x = Math.pow(u1, 1.0 / alpha);
        double y = Math.pow(u2, 1.0 / beta);
        return x / (x + y);
    }

    /**
     * Calculates a realistic time a human might take to "read" content of a certain
     * length.
     */
    public static Duration calculateReadingDelay(int contentLength,
            SeleniumProperties.Humanization.DelayRange baseDelay) {
        long baseMs = baseDelay.getMin().toMillis();
        long maxMs = baseDelay.getMax().toMillis();
        long contentMs = Math.min(contentLength / 10, 3000); // 1ms per 10 chars, capped
        long totalMinMs = baseMs + contentMs;
        long totalMaxMs = maxMs + contentMs;
        long randomMs = generateHumanTiming(totalMinMs, totalMaxMs, 1.0); // No momentum for reading
        return Duration.ofMillis(randomMs);
    }

    /**
     * Simple sleep utility that handles InterruptedException.
     */
    public static void sleep(long milliseconds) {
        if (milliseconds <= 0) {
            return;
        }
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            logger.warn("Sleep interrupted: {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }
}