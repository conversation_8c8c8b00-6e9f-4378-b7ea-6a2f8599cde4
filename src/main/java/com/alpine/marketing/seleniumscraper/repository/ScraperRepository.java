package com.alpine.marketing.seleniumscraper.repository;

import com.alpine.marketing.seleniumscraper.dto.ScrapedContactInfo;
import com.alpine.marketing.seleniumscraper.dto.ScrapingTaskDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ScraperRepository {

    private static final Logger logger = LoggerFactory.getLogger(ScraperRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public ScraperRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<ScrapingTaskDto> getAllUnscannedUrls(String tag) {
        StringBuilder sql = new StringBuilder("""
                SELECT sr.id, sr.urn, s.email_domain, sr.url
                FROM search_result sr
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN school s ON sr.urn = s.urn
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE sr.scanned IS NULL
                AND st.search_site NOT LIKE '%linkedin.com%'
                """);

        List<Object> params = new ArrayList<>();
        addFilteringClauses(sql, params, tag);

        sql.append(" ORDER BY sq.priority ASC, sr.urn, sr.created");

        try {
            List<ScrapingTaskDto> urlInfos = jdbcTemplate.query(sql.toString(), new ScrapingTaskDtoRowMapper(),
                    params.toArray());
            logger.info("Found {} unscanned non-LinkedIn URLs to process for tag '{}'",
                    urlInfos.size(), tag != null ? tag : "all");
            return urlInfos;
        } catch (Exception e) {
            logger.error("Failed to fetch all unscanned URLs: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch unscanned URLs", e);
        }
    }

    public void markUrlAsScanned(int searchResultId) {
        String sql = "UPDATE search_result SET scanned = ? WHERE id = ?";
        try {
            jdbcTemplate.update(sql, Timestamp.from(Instant.now()), searchResultId);
        } catch (Exception e) {
            logger.error("Failed to mark URL with ID {} as scanned: {}", searchResultId, e.getMessage(), e);
            throw new RuntimeException("Failed to mark URL as scanned", e);
        }
    }

    @Transactional
    public Long saveScrapedContact(ScrapedContactInfo contactInfo) {
        String sql = """
                INSERT INTO school_scraped_email (urn, email, source, search_result_id)
                VALUES (?, ?, ?, ?)
                """;
        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, contactInfo.getUrn());
                ps.setString(2, contactInfo.getEmail());
                ps.setString(3, contactInfo.getSourceUrl());
                ps.setInt(4, contactInfo.getSearchResultId());
                return ps;
            }, keyHolder);

            return extractIdFromKeyHolder(keyHolder, contactInfo.getEmail());
        } catch (Exception e) {
            logger.error("Failed to save scraped contact for URN {}: {}", contactInfo.getUrn(), e.getMessage());
            throw new RuntimeException("Failed to save scraped contact", e);
        }
    }

    private Long extractIdFromKeyHolder(KeyHolder keyHolder, String email) {
        Number key = keyHolder.getKey();
        if (key != null) {
            return key.longValue();
        } else {
            logger.warn("Insert for email '{}' did not return a generated ID.", email);
            return null;
        }
    }

    public int countTotalUrls(String tag) {
        return countUrlsByScannedStatus(tag, null);
    }

    public int countScannedUrls(String tag) {
        return countUrlsByScannedStatus(tag, true);
    }

    public int countUnscannedUrls(String tag) {
        return countUrlsByScannedStatus(tag, false);
    }

    private int countUrlsByScannedStatus(String tag, Boolean isScanned) {
        StringBuilder sql = new StringBuilder("""
                SELECT COUNT(*) FROM search_result sr
                JOIN search_task st ON sr.search_task_id = st.id
                JOIN search_query sq ON st.search_query_id = sq.id
                WHERE st.search_site NOT LIKE '%linkedin.com%'
                """);
        List<Object> params = new ArrayList<>();
        if (isScanned != null) {
            sql.append(isScanned ? " AND sr.scanned IS NOT NULL" : " AND sr.scanned IS NULL");
        }
        addFilteringClauses(sql, params, tag);

        try {
            Integer count = jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Failed to count URLs: {}", e.getMessage(), e);
            return 0;
        }
    }

    public int countScrapedEmails() {
        String sql = "SELECT COUNT(*) FROM school_scraped_email";
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Failed to count scraped emails: {}", e.getMessage(), e);
            return 0;
        }
    }

    private void addFilteringClauses(StringBuilder sql, List<Object> params, String tag) {
        if (tag != null && !tag.trim().isEmpty()) {
            sql.append(" AND sq.tag = ?");
            params.add(tag);
        }
    }

    private static class ScrapingTaskDtoRowMapper implements RowMapper<ScrapingTaskDto> {
        @Override
        public ScrapingTaskDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return new ScrapingTaskDto(
                    rs.getInt("id"),
                    rs.getInt("urn"),
                    rs.getString("url"),
                    rs.getString("email_domain"));
        }
    }
}