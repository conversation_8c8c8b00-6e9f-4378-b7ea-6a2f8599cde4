package com.alpine.marketing.seleniumscraper.service;

import com.alpine.marketing.seleniumscraper.config.SeleniumProperties;
import com.alpine.marketing.seleniumscraper.dto.HumanizationSession;
import com.alpine.marketing.seleniumscraper.util.MouseAndKeyUtil;
import com.alpine.marketing.seleniumscraper.util.TimingUtil;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.interactions.Actions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Random;

/**
 * Orchestrates high-level human-like behavior simulations in Selenium
 * WebDriver.
 * Uses utility classes for detailed interactions like timing, scrolling, and
 * input.
 */
public final class HumanBehaviorSimulator {

    private static final Logger logger = LoggerFactory.getLogger(HumanBehaviorSimulator.class);
    private static final Random random = new Random();

    private HumanBehaviorSimulator() {
    }

    /**
     * Simulates a comprehensive page interaction, including reading, scrolling, and
     * mouse movement.
     */
    public static void simulatePageInteraction(WebDriver driver, SeleniumProperties.Humanization humanization,
            HumanizationSession session) {
        if (!humanization.isEnabled()) {
            return;
        }

        logger.info("Starting enhanced page interaction simulation. {}", session.getStats());

        TimingUtil.randomDelay(humanization.getPageLoadDelay().getMin(), humanization.getPageLoadDelay().getMax(),
                session);
        simulateReadingBehavior(driver);

        if (random.nextDouble() < 0.7 && humanization.getScrollBehavior().isEnabled()) {
            simulateScrolling(driver, humanization.getScrollBehavior(), session);
        }

        if (random.nextDouble() < 0.3 && humanization.getMouseBehavior().isEnabled()) {
            MouseAndKeyUtil.simulateMouseMovement(new Actions(driver), humanization.getMouseBehavior(), session);
        }

        if (random.nextDouble() < 0.15) {
            simulateSecondLook(driver, session);
        }

        logger.info("Completed enhanced page interaction simulation. {}", session.getStats());
    }

    private static void simulateReadingBehavior(WebDriver driver) {
        if (!(driver instanceof JavascriptExecutor))
            return;
        try {
            Long contentLength = (Long) ((JavascriptExecutor) driver)
                    .executeScript("return document.body ? document.body.innerText.length : 0");
            if (contentLength != null && contentLength > 100) {
                long readingTimeMs = Math.min(3000, (contentLength / 5) * 60000 / 250); // 250 WPM
                readingTimeMs = (long) (readingTimeMs * (0.6 + random.nextDouble() * 0.8));
                if (readingTimeMs > 500) {
                    TimingUtil.sleep(readingTimeMs);
                    logger.info("Simulated reading time: {}ms for {} characters", readingTimeMs, contentLength);
                }
            }
        } catch (Exception e) {
            logger.info("Could not simulate reading behavior: {}", e.getMessage());
        }
    }

    private static void simulateSecondLook(WebDriver driver, HumanizationSession session) {
        if (!(driver instanceof JavascriptExecutor))
            return;
        try {
            Long currentScroll = (Long) ((JavascriptExecutor) driver).executeScript("return window.pageYOffset");
            if (currentScroll != null && currentScroll > 100) {
                ((JavascriptExecutor) driver).executeScript("window.scrollBy(0, arguments[0]);",
                        -(random.nextInt((int) (currentScroll / 2))));
                TimingUtil.randomDelay(Duration.ofMillis(800), Duration.ofMillis(2500), session);
                ((JavascriptExecutor) driver).executeScript("window.scrollTo(0, arguments[0]);", currentScroll);
                logger.info("Simulated 'second look' behavior");
            }
        } catch (Exception e) {
            logger.info("Could not simulate second look: {}", e.getMessage());
        }
    }

    public static void simulateScrolling(WebDriver driver,
            SeleniumProperties.Humanization.ScrollBehavior scrollBehavior, HumanizationSession session) {
        if (!(driver instanceof JavascriptExecutor))
            return;
        try {
            Long viewportHeight = (Long) ((JavascriptExecutor) driver).executeScript("return window.innerHeight");
            Long pageHeight = (Long) ((JavascriptExecutor) driver).executeScript("return document.body.scrollHeight");
            if (viewportHeight == null || pageHeight == null || pageHeight <= viewportHeight)
                return;

            int totalScrolls = 2 + random.nextInt(scrollBehavior.getMaxScrolls());
            for (int i = 0; i < totalScrolls; i++) {
                double scrollFactor = 0.2 + (random.nextDouble() * 0.6);
                int scrollAmount = (int) (viewportHeight * scrollFactor);
                ((JavascriptExecutor) driver).executeScript("window.scrollBy(0, arguments[0]);", scrollAmount);
                TimingUtil.randomDelay(scrollBehavior.getDelay().getMin(), scrollBehavior.getDelay().getMax(), session);
            }
            logger.info("Completed explorative scrolling pattern");
        } catch (Exception e) {
            logger.warn("Error during enhanced scroll simulation: {}", e.getMessage());
        }
    }
}