package com.alpine.marketing.seleniumscraper.service;

import com.alpine.marketing.seleniumscraper.dto.HumanizationSession;
import com.alpine.marketing.seleniumscraper.dto.ScrapedContactInfo;
import com.alpine.marketing.seleniumscraper.dto.ScrapingTaskDto;
import com.alpine.marketing.seleniumscraper.repository.ScraperRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * Orchestrates the Selenium scraping workflow.
 * Fetches unscanned URLs, manages WebDriver instances, and processes pages.
 */
@Service
public class SeleniumScraperService {

    private static final Logger logger = LoggerFactory.getLogger(SeleniumScraperService.class);

    private final WebDriverPoolService webDriverPoolService;
    private final ScraperRepository scraperRepository;
    private final DocumentProcessingService documentProcessingService;
    private final EmailValidationService emailValidationService;

    public SeleniumScraperService(WebDriverPoolService webDriverPoolService,
            ScraperRepository scraperRepository,
            DocumentProcessingService documentProcessingService,
            EmailValidationService emailValidationService) {
        this.webDriverPoolService = webDriverPoolService;
        this.scraperRepository = scraperRepository;
        this.documentProcessingService = documentProcessingService;
        this.emailValidationService = emailValidationService;
    }

    /**
     * Main method to execute the email scraping workflow.
     */
    public ScrapingResult executeEmailScraping(String tag) {
        HumanizationSession session = new HumanizationSession();
        List<ScrapingTaskDto> tasksToScrape = scraperRepository.getAllUnscannedUrls(tag);
        if (tasksToScrape.isEmpty()) {
            logger.info("No unscanned URLs to process for the given filters. Exiting.");
            return ScrapingResult.success(0, 0);
        }

        logger.info("Starting to scrape {} URLs...", tasksToScrape.size());
        int urlsProcessed = 0;
        int emailsFound = 0;

        for (ScrapingTaskDto task : tasksToScrape) {
            try {
                Set<String> scrapedEmails;

                if (documentProcessingService.isDocumentUrl(task.getUrl())) {
                    // Process documents without a WebDriver instance for efficiency
                    scrapedEmails = documentProcessingService.extractEmailsFromDocument(task.getUrl());
                } else {
                    // Use a WebDriver from the pool only for webpages
                    scrapedEmails = webDriverPoolService
                            .executeWithDriver(driver -> {
                                HumanBehaviorSimulator.simulatePageInteraction(driver,
                                        webDriverPoolService.getSeleniumProperties().getHumanization(), session);
                                return documentProcessingService.extractEmailsFromWebpage(driver,
                                        task.getUrl());
                            });
                }

                if (!scrapedEmails.isEmpty()) {
                    logger.info("Found {} raw emails on URL: {}", scrapedEmails.size(), task.getUrl());

                    int validEmailsFound = 0;
                    for (String email : scrapedEmails) {
                        EmailValidationService.ValidationResult validationResult = emailValidationService
                                .validateEmail(email);

                        if (validationResult.isValid()) {
                            ScrapedContactInfo contactInfo = new ScrapedContactInfo(
                                    validationResult.getCleanEmail(), // Use the cleaned email
                                    task.getUrl(),
                                    task.getSearchResultId(),
                                    task.getUrn());
                            scraperRepository.saveScrapedContact(contactInfo);
                            validEmailsFound++;
                            logger.info("  ✅ Saved valid human email: {} (Type: {})",
                                    validationResult.getCleanEmail(), validationResult.getEmailType());
                        } else {
                            // Optionally log why an email was rejected for debugging
                            logger.info("  ❌ Rejected email '{}': {}", email, validationResult.getReason());
                        }
                    }

                    if (validEmailsFound > 0) {
                        emailsFound += validEmailsFound;
                        logger.info("Saved {} valid human emails from URL: {}", validEmailsFound, task.getUrl());
                    }
                }

                // Mark URL as scanned after processing
                scraperRepository.markUrlAsScanned(task.getSearchResultId());
                urlsProcessed++;

            } catch (Exception e) {
                logger.error("Failed to process URL {}: {}", task.getUrl(), e.getMessage());
                // Mark as scanned even on failure to avoid retrying problematic URLs
                scraperRepository.markUrlAsScanned(task.getSearchResultId());
            }
        }

        logger.info("Scraping finished. Processed {} URLs and found {} emails.",
                urlsProcessed, emailsFound);
        return ScrapingResult.success(urlsProcessed, emailsFound);
    }

    public static class ScrapingResult {
        private final boolean successful;
        private final int urlsProcessed;
        private final int emailsFound;
        private final String error;

        private ScrapingResult(boolean successful, int urlsProcessed, int emailsFound, String error) {
            this.successful = successful;
            this.urlsProcessed = urlsProcessed;
            this.emailsFound = emailsFound;
            this.error = error;
        }

        public static ScrapingResult success(int urlsProcessed, int emailsFound) {
            return new ScrapingResult(true, urlsProcessed, emailsFound, null);
        }

        public static ScrapingResult failure(String error) {
            return new ScrapingResult(false, 0, 0, error);
        }

        public boolean hasError() {
            return error != null;
        }

        // Getters and toString...
        public boolean isSuccessful() {
            return successful;
        }

        public int getUrlsProcessed() {
            return urlsProcessed;
        }

        public int getEmailsFound() {
            return emailsFound;
        }

        public String getError() {
            return error;
        }

        @Override
        public String toString() {
            if (successful) {
                return String.format("Success - Processed %d URLs, Found %d emails", urlsProcessed, emailsFound);
            } else {
                return String.format("Failure - Error: %s", error);
            }
        }
    }
}