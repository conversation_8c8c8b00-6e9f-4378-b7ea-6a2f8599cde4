package com.alpine.marketing.seleniumscraper.service;

import com.alpine.marketing.seleniumscraper.config.SeleniumProperties;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service for managing a pool of Selenium WebDriver instances.
 * Provides efficient reuse of WebDriver instances for email scraping
 * operations.
 */
@Service
public class WebDriverPoolService {

    private static final Logger logger = LoggerFactory.getLogger(WebDriverPoolService.class);

    private final SeleniumProperties seleniumProperties;
    private final WebDriverFactory webDriverFactory;
    private final BlockingQueue<WebDriver> driverPool;
    private final AtomicInteger createdDrivers;
    private volatile boolean isShutdown = false;

    public WebDriverPoolService(SeleniumProperties seleniumProperties, WebDriverFactory webDriverFactory) {
        this.seleniumProperties = seleniumProperties;
        this.webDriverFactory = webDriverFactory;
        this.driverPool = new LinkedBlockingQueue<>(seleniumProperties.getPool().getMaxSize());
        this.createdDrivers = new AtomicInteger(0);
        initializePool();
    }

    public SeleniumProperties getSeleniumProperties() {
        return seleniumProperties;
    }

    /**
     * Initialize the driver pool with initial instances.
     */
    private void initializePool() {
        int initialPoolSize = seleniumProperties.getPool().getInitialSize();
        for (int i = 0; i < initialPoolSize; i++) {
            try {
                WebDriver driver = webDriverFactory.createDriver();
                driverPool.offer(driver);
                createdDrivers.incrementAndGet();
                logger.info("🚗 Created initial WebDriver #{}", i + 1);
            } catch (Exception e) {
                logger.error("Failed to create initial WebDriver #{}: {}", i + 1, e.getMessage());
            }
        }
        logger.info("🚗 WebDriver pool initialized with {} drivers (max: {})",
                driverPool.size(), seleniumProperties.getPool().getMaxSize());
    }

    /**
     * Acquire a WebDriver from the pool.
     * Creates a new driver if pool is empty and under max capacity.
     *
     * @return WebDriver instance
     * @throws RuntimeException if no driver can be obtained
     */
    public WebDriver acquireDriver() {
        if (isShutdown) {
            throw new IllegalStateException("WebDriver pool has been shut down");
        }

        WebDriver driver = driverPool.poll();

        if (driver != null) {
            // Check if driver is still responsive
            if (WebDriverFactory.isDriverResponsive(driver)) {
                logger.info("🚗 Acquired WebDriver from pool (remaining: {})", driverPool.size());
                return driver;
            } else {
                logger.warn("🚗 Found unresponsive driver in pool, creating new one");
                WebDriverFactory.quitDriver(driver);
                createdDrivers.decrementAndGet();
            }
        }

        // Create new driver if pool is empty and under capacity
        if (createdDrivers.get() < seleniumProperties.getPool().getMaxSize()) {
            try {
                driver = webDriverFactory.createDriver();
                createdDrivers.incrementAndGet();
                logger.info("🚗 Created new WebDriver (total created: {})", createdDrivers.get());
                return driver;
            } catch (Exception e) {
                logger.error("Failed to create new WebDriver: {}", e.getMessage());
                throw new RuntimeException("Failed to acquire WebDriver", e);
            }
        }

        throw new RuntimeException("No WebDriver available and pool is at maximum capacity");
    }

    /**
     * Release a WebDriver back to the pool.
     * If the driver is unresponsive, it will be discarded.
     *
     * @param driver The WebDriver to release
     */
    public void releaseDriver(WebDriver driver) {
        if (driver == null || isShutdown) {
            return;
        }

        if (WebDriverFactory.isDriverResponsive(driver)) {
            boolean offered = driverPool.offer(driver);
            if (offered) {
                logger.info("🚗 Released WebDriver to pool (pool size: {})", driverPool.size());
            } else {
                // Pool is full, quit the driver
                logger.info("🚗 Pool is full, quitting excess WebDriver");
                WebDriverFactory.quitDriver(driver);
                createdDrivers.decrementAndGet();
            }
        } else {
            logger.warn("🚗 Discarding unresponsive WebDriver");
            WebDriverFactory.quitDriver(driver);
            createdDrivers.decrementAndGet();
        }
    }

    /**
     * Execute a task with a WebDriver from the pool.
     * Automatically handles driver acquisition and release.
     *
     * @param task The task to execute with the WebDriver
     * @param <T>  Return type of the task
     * @return Result of the task execution
     */
    public <T> T executeWithDriver(WebDriverTask<T> task) {
        WebDriver driver = null;
        try {
            driver = acquireDriver();
            return task.execute(driver);
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException("Task execution failed", e);
        } finally {
            if (driver != null) {
                releaseDriver(driver);
            }
        }
    }

    /**
     * Get current pool statistics.
     */
    public PoolStats getPoolStats() {
        return new PoolStats(
                driverPool.size(),
                createdDrivers.get(),
                seleniumProperties.getPool().getMaxSize(),
                isShutdown);
    }

    /**
     * Shutdown the pool and quit all drivers.
     */
    @PreDestroy
    public void shutdown() {
        logger.info("🚗 Shutting down WebDriver pool...");
        isShutdown = true;

        WebDriver driver;
        int quitCount = 0;
        while ((driver = driverPool.poll()) != null) {
            WebDriverFactory.quitDriver(driver);
            quitCount++;
        }

        logger.info("🚗 WebDriver pool shutdown complete. Quit {} drivers.", quitCount);
        createdDrivers.set(0);
    }

    /**
     * Functional interface for tasks that use WebDriver.
     */
    @FunctionalInterface
    public interface WebDriverTask<T> {
        T execute(WebDriver driver) throws Exception;
    }

    /**
     * Pool statistics for monitoring.
     */
    public static class PoolStats {
        private final int availableDrivers;
        private final int totalCreated;
        private final int maxPoolSize;
        private final boolean isShutdown;

        public PoolStats(int availableDrivers, int totalCreated, int maxPoolSize, boolean isShutdown) {
            this.availableDrivers = availableDrivers;
            this.totalCreated = totalCreated;
            this.maxPoolSize = maxPoolSize;
            this.isShutdown = isShutdown;
        }

        public int getAvailableDrivers() {
            return availableDrivers;
        }

        public int getTotalCreated() {
            return totalCreated;
        }

        public int getMaxPoolSize() {
            return maxPoolSize;
        }

        public boolean isShutdown() {
            return isShutdown;
        }

        public int getInUse() {
            return totalCreated - availableDrivers;
        }

        @Override
        public String toString() {
            return String.format("PoolStats{available=%d, inUse=%d, total=%d, max=%d, shutdown=%s}",
                    availableDrivers, getInUse(), totalCreated, maxPoolSize, isShutdown);
        }
    }
}