package com.alpine.marketing.seleniumscraper.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Email validation service focused on capturing human emails for marketing
 * purposes.
 * Adapted from the original EmailValidationService in the main email-app
 * project.
 * 
 * Prioritizes personal name-based emails and job/role-based emails over generic
 * contact emails. Optimized for UK schools to capture staff and recruitment
 * emails.
 */
@Service
public class EmailValidationService {

    private static final Logger logger = LoggerFactory.getLogger(EmailValidationService.class);

    // Staff title prefixes commonly used in UK schools
    private static final List<String> STAFF_TITLE_PREFIXES = Arrays.asList(
            "mr", "mrs", "miss", "ms", "dr", "prof", "sir", "dame");

    // Role-based emails that represent ACTUAL PEOPLE - these should be INCLUDED
    private static final Set<String> ROLE_BASED_HUMAN_EMAILS = new HashSet<>(Arrays.asList(
            // Senior leadership (actual people)
            "headteacher", "principal", "deputy", "head", "vice", "assistant",
            // Administrative staff (actual people)
            "secretary", "clerk", "registrar", "bursar", "treasurer",
            // HR and recruitment (actual people)
            "recruitment", "hr", "humanresources", "careers", "jobs",
            // Student services (actual people)
            "admissions", "registrations", "safeguarding", "welfare",
            // Academic staff roles (actual people)
            "senco", "coordinator", "librarian", "examsofficer"));

    // System/automated emails to EXCLUDE (not actual people)
    private static final Set<String> SYSTEM_AUTOMATED_EMAILS = new HashSet<>(Arrays.asList(
            // Generic contact points
            "info", "enquiries", "enquiry", "admin", "office", "reception",
            "contact", "support", "help", "general", "main", "school",
            // Automated systems
            "noreply", "no-reply", "donotreply", "do-not-reply",
            "mailer-daemon", "postmaster", "abuse", "spam",
            "test", "example", "sample", "dummy", "fake",
            "webmaster", "hostmaster", "listserv", "majordomo"));

    // Departmental/facility emails to EXCLUDE (not individual people)
    private static final Set<String> DEPARTMENTAL_PATTERNS = new HashSet<>(Arrays.asList(
            "department", "dept", "faculty", "lab", "laboratory",
            "library", "centre", "center", "unit", "team", "group",
            "office", "services", "studies", "programme", "program"));

    // Common invalid domains to exclude
    private static final Set<String> INVALID_DOMAINS = new HashSet<>(Arrays.asList(
            "example.com", "example.org", "example.net",
            "test.com", "localhost", "127.0.0.1",
            "sentry.io", "gravatar.com", "facebook.com", "twitter.com",
            "linkedin.com", "youtube.com", "instagram.com"));

    // Patterns for invalid characters/sequences that shouldn't be in emails
    private static final Set<String> INVALID_SEQUENCES = new HashSet<>(Arrays.asList(
            "%22", "%40", "%2B", "%2E", "%20", // URL encoded characters
            "++", "..", "@@", "--", // Double characters
            "+@", ".@", "-@", // Invalid before @
            "@.", "@+", "@-" // Invalid after @
    ));

    // Email format patterns
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    private static final Pattern SCHOOL_EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]*\\.(ac\\.uk|edu|edu\\.[a-z]{2}|sch\\.uk|co\\.uk)$");

    // Pattern for personal names (supports numbers, hyphens, underscores)
    private static final Pattern PERSONAL_NAME_PATTERN = Pattern.compile(
            "^[a-z][a-z0-9]*([._-][a-z][a-z0-9]*)*$");

    // Pattern for initials (2-4 characters, optionally separated)
    private static final Pattern INITIALS_PATTERN = Pattern.compile(
            "^[a-z]{1,2}([._-]?[a-z]{1,2})?$");

    // Pattern for initial + surname combinations (j.smith, m-jones, etc.)
    private static final Pattern INITIAL_SURNAME_PATTERN = Pattern.compile(
            "^[a-z][._-][a-z]{2,}[a-z0-9]*$");

    /**
     * Comprehensive email validation focused on human emails
     */
    public ValidationResult validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return ValidationResult.invalid("Email is null or empty");
        }

        // Step 1: Clean and decode the email
        String cleanEmail = cleanAndDecodeEmail(email);
        if (cleanEmail == null) {
            return ValidationResult.invalid("Email could not be cleaned");
        }

        // Step 2: Check for invalid sequences before validation
        if (containsInvalidSequences(cleanEmail)) {
            return ValidationResult.invalid("Contains invalid character sequences");
        }

        // Step 3: Basic format validation
        if (!isValidFormat(cleanEmail)) {
            return ValidationResult.invalid("Invalid email format");
        }

        // Step 4: Check for invalid patterns
        if (containsSystemAutomatedPattern(cleanEmail)) {
            return ValidationResult.invalid("Contains system/automated pattern (noreply, test, etc.)");
        }

        // Step 5: Check for invalid domains
        if (hasInvalidDomain(cleanEmail)) {
            return ValidationResult.invalid("Invalid or excluded domain");
        }

        // Step 6: Check if it's a human email (PRIORITY CHANGE)
        if (!isHumanEmail(cleanEmail)) {
            return ValidationResult
                    .invalid("Not a human email (we want personal/staff emails, not generic contact emails)");
        }

        // Step 7: Check if it's a school email (preferred)
        boolean isSchoolEmail = isSchoolEmail(cleanEmail);

        // Step 8: Determine email type for priority
        EmailType emailType = getEmailType(cleanEmail);

        return ValidationResult.valid(cleanEmail, isSchoolEmail, emailType);
    }

    /**
     * Validate email against a specific domain
     */
    public ValidationResult validateEmailForDomain(String email, String expectedDomain) {
        ValidationResult basicValidation = validateEmail(email);

        if (!basicValidation.isValid()) {
            return basicValidation;
        }

        // Check domain match
        if (expectedDomain != null && !isEmailValidForDomain(basicValidation.getCleanEmail(), expectedDomain)) {
            return ValidationResult.invalid("Email domain does not match expected domain: " + expectedDomain);
        }

        return basicValidation;
    }

    /**
     * Check if email belongs to the expected domain
     */
    public boolean isEmailValidForDomain(String email, String expectedDomain) {
        if (email == null || expectedDomain == null) {
            return false;
        }

        String emailLower = email.toLowerCase();
        String domainLower = expectedDomain.toLowerCase();

        return emailLower.endsWith("@" + domainLower);
    }

    /**
     * Check if email is a human email - represents an actual person.
     * 
     * INCLUDES:
     * - Staff titles: mr.smith@, dr.jones@
     * - Role-based emails: headteacher@, recruitment@, secretary@
     * - Personal names: john.smith@, mary.jones123@, j.adams@
     * - Initials: rm@, j.b@, mp@
     * - Enhanced patterns: john-smith@, mary_jones@, smith2024@
     * 
     * EXCLUDES:
     * - System/automated: info@, noreply@, webmaster@
     * - Departmental: maths.department@, science.lab@
     * - Overly generic: a@, x.y@, 1.2@
     */
    public boolean isHumanEmail(String email) {
        if (email == null || !email.contains("@")) {
            return false;
        }

        String localPart = email.substring(0, email.indexOf('@')).toLowerCase().trim();

        // 1. EXCLUDE: System/automated emails (not actual people)
        if (SYSTEM_AUTOMATED_EMAILS.contains(localPart)) {
            return false;
        }

        // 2. EXCLUDE: Departmental/facility emails (check if contains departmental
        // keywords)
        if (isDepartmentalEmail(localPart)) {
            return false;
        }

        // 3. EXCLUDE: Overly generic patterns (prevent false positives)
        if (isOverlyGeneric(localPart)) {
            return false;
        }

        // 4. INCLUDE: Role-based emails representing actual people
        if (ROLE_BASED_HUMAN_EMAILS.contains(localPart)) {
            return true;
        }

        // 5. INCLUDE: Staff title-based emails (actual people with titles)
        if (STAFF_TITLE_PREFIXES.stream().anyMatch(prefix -> localPart.startsWith(prefix + "."))) {
            return true;
        }

        // 6. INCLUDE: Initial + surname patterns (j.smith, m-jones, etc.)
        if (INITIAL_SURNAME_PATTERN.matcher(localPart).matches()) {
            return true;
        }

        // 7. INCLUDE: Simple initials (rm, jb, mp) but with length validation
        if (INITIALS_PATTERN.matcher(localPart).matches()) {
            return true;
        }

        // 8. INCLUDE: Enhanced personal name patterns (supports numbers, separators)
        if (PERSONAL_NAME_PATTERN.matcher(localPart).matches()) {
            return true;
        }

        // 9. INCLUDE: Multi-part name patterns (firstname.lastname, firstname_lastname)
        if (isValidMultiPartName(localPart)) {
            return true;
        }

        return false;
    }

    /**
     * Get email type for prioritization (human emails only)
     */
    public EmailType getEmailType(String email) {
        if (email == null || !email.contains("@")) {
            return EmailType.INVALID;
        }

        String localPart = email.substring(0, email.indexOf('@')).toLowerCase().trim();

        // Role-based emails (headteacher, recruitment, etc.)
        if (ROLE_BASED_HUMAN_EMAILS.contains(localPart)) {
            return EmailType.ROLE_BASED;
        }

        // Staff title-based emails (actual people with titles)
        if (STAFF_TITLE_PREFIXES.stream().anyMatch(prefix -> localPart.startsWith(prefix + "."))) {
            return EmailType.STAFF_TITLE;
        }

        // Personal name-based emails
        if (PERSONAL_NAME_PATTERN.matcher(localPart).matches() ||
                INITIAL_SURNAME_PATTERN.matcher(localPart).matches() ||
                isValidMultiPartName(localPart)) {
            return EmailType.PERSONAL_NAME;
        }

        // Initials
        if (INITIALS_PATTERN.matcher(localPart).matches()) {
            return EmailType.INITIALS;
        }

        return EmailType.OTHER_HUMAN;
    }

    /**
     * Check if email contains departmental/facility keywords.
     */
    private boolean isDepartmentalEmail(String localPart) {
        return DEPARTMENTAL_PATTERNS.stream()
                .anyMatch(pattern -> localPart.contains(pattern));
    }

    /**
     * Check if email pattern is overly generic (prevent false positives).
     */
    private boolean isOverlyGeneric(String localPart) {
        // Reject single character parts
        if (localPart.length() <= 1) {
            return true;
        }

        // Reject patterns like "a.b", "x.y", "1.2" (too generic)
        if (localPart.matches("^[a-z0-9][._-][a-z0-9]$")) {
            return true;
        }

        // Reject purely numeric patterns
        if (localPart.matches("^[0-9]+([._-][0-9]+)*$")) {
            return true;
        }

        return false;
    }

    /**
     * Validate multi-part name patterns (firstname.lastname, firstname_lastname).
     * Enhanced to support numbers and better validation.
     */
    private boolean isValidMultiPartName(String localPart) {
        if (!localPart.contains(".") && !localPart.contains("_") && !localPart.contains("-")) {
            return false;
        }

        String[] parts = localPart.split("[._-]");
        if (parts.length < 2 || parts.length > 4) {
            return false; // Support 2-4 parts
        }

        // Each part should be at least 2 characters and start with a letter
        for (String part : parts) {
            if (part.length() < 2 || !Character.isLetter(part.charAt(0))) {
                return false;
            }
            // Allow letters and numbers, but must start with letter
            if (!part.matches("^[a-z][a-z0-9]*$")) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate and clean email for database storage
     */
    public String validateAndCleanEmail(String email) {
        ValidationResult result = validateEmail(email);
        return result.isValid() ? result.getCleanEmail() : null;
    }

    /**
     * Check if email is valid for our use case (human emails)
     */
    public boolean isValidEmail(String email) {
        return validateEmail(email).isValid();
    }

    /**
     * Check if email is a school email (preferred)
     */
    public boolean isSchoolEmail(String email) {
        if (email == null)
            return false;
        return SCHOOL_EMAIL_PATTERN.matcher(email.toLowerCase()).matches();
    }

    /**
     * Clean and decode email address from various encodings
     */
    private String cleanAndDecodeEmail(String email) {
        if (email == null)
            return null;

        try {
            // Step 1: Trim and convert to lowercase
            String cleaned = email.trim().toLowerCase();

            // Step 2: URL decode to handle %22, %40, etc.
            try {
                cleaned = URLDecoder.decode(cleaned, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                logger.info("Failed to URL decode email: {}", e.getMessage());
            }

            // Step 3: Clean HTML entities
            cleaned = cleaned.replace("&quot;", "") // Remove quotes
                    .replace("&amp;", "&")
                    .replace("&lt;", "") // Remove <
                    .replace("&gt;", "") // Remove >
                    .replace("&#64;", "@") // Fix @ symbol
                    .replace("&#46;", ".") // Fix . symbol
                    .replace("&nbsp;", ""); // Remove spaces

            // Step 4: Remove common URL encoded sequences
            cleaned = cleaned.replace("%22", "") // Remove URL encoded quotes
                    .replace("%3c", "") // Remove URL encoded <
                    .replace("%3e", "") // Remove URL encoded >
                    .replace("%20", "") // Remove URL encoded spaces
                    .replace("%2b", "+") // Fix URL encoded +
                    .replace("%2e", ".") // Fix URL encoded .
                    .replace("%40", "@"); // Fix URL encoded @

            // Step 5: Fix specific patterns from scraped data
            // Pattern: "domain+%22@domain" should become "domain@domain"
            cleaned = cleaned.replaceAll("\\+\\s*@", "@"); // Remove + before @
            cleaned = cleaned.replaceAll("\\s+@", "@"); // Remove spaces before @
            cleaned = cleaned.replaceAll("@\\s+", "@"); // Remove spaces after @

            // Step 6: Remove any remaining HTML tags or brackets
            cleaned = cleaned.replaceAll("<[^>]*>", "") // Remove HTML tags
                    .replaceAll("[<>\"']", "") // Remove remaining brackets/quotes
                    .replaceAll("\\s+", "") // Remove all extra spaces
                    .trim();

            // Step 7: Basic validation - must contain @ and be reasonable length
            if (!cleaned.contains("@") || cleaned.length() < 5 || cleaned.length() > 254) {
                return null;
            }

            return cleaned;

        } catch (Exception e) {
            logger.info("Error cleaning email '{}': {}", email, e.getMessage());
            return null;
        }
    }

    /**
     * Check for invalid character sequences
     */
    private boolean containsInvalidSequences(String email) {
        return INVALID_SEQUENCES.stream().anyMatch(email::contains);
    }

    private boolean isValidFormat(String email) {
        return EMAIL_PATTERN.matcher(email).matches();
    }

    private boolean containsSystemAutomatedPattern(String email) {
        String localPart = email.substring(0, email.indexOf('@')).toLowerCase();
        return SYSTEM_AUTOMATED_EMAILS.stream().anyMatch(localPart::contains);
    }

    private boolean hasInvalidDomain(String email) {
        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();
        return INVALID_DOMAINS.contains(domain);
    }

    /**
     * Email type enumeration for human emails
     */
    public enum EmailType {
        ROLE_BASED(1), // headteacher@, recruitment@ - highest priority (role-based people)
        STAFF_TITLE(2), // mr.smith@, dr.jones@ - high priority (people with titles)
        PERSONAL_NAME(3), // sjones@, john.smith@ - high priority (personal names)
        INITIALS(4), // rm@, j.b@ - medium priority (likely personal initials)
        OTHER_HUMAN(5), // other human emails - low priority
        INVALID(0); // invalid emails

        private final int priority;

        EmailType(int priority) {
            this.priority = priority;
        }

        public int getPriority() {
            return priority;
        }
    }

    /**
     * Validation result class
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String cleanEmail;
        private final String reason;
        private final boolean isSchoolEmail;
        private final EmailType emailType;

        private ValidationResult(boolean valid, String cleanEmail, String reason, boolean isSchoolEmail,
                EmailType emailType) {
            this.valid = valid;
            this.cleanEmail = cleanEmail;
            this.reason = reason;
            this.isSchoolEmail = isSchoolEmail;
            this.emailType = emailType;
        }

        public static ValidationResult valid(String cleanEmail, boolean isSchoolEmail, EmailType emailType) {
            return new ValidationResult(true, cleanEmail, null, isSchoolEmail, emailType);
        }

        public static ValidationResult invalid(String reason) {
            return new ValidationResult(false, null, reason, false, EmailType.INVALID);
        }

        public boolean isValid() {
            return valid;
        }

        public String getCleanEmail() {
            return cleanEmail;
        }

        public String getReason() {
            return reason;
        }

        public boolean isSchoolEmail() {
            return isSchoolEmail;
        }

        public EmailType getEmailType() {
            return emailType;
        }

        @Override
        public String toString() {
            return valid
                    ? String.format("Valid human email: %s (school: %s, type: %s)", cleanEmail, isSchoolEmail,
                            emailType)
                    : String.format("Invalid email: %s", reason);
        }
    }
}