package com.alpine.marketing.seleniumscraper.service;

import com.alpine.marketing.seleniumscraper.config.SeleniumProperties;
import com.alpine.marketing.seleniumscraper.util.PlatformDetector;
import com.alpine.marketing.seleniumscraper.util.WebDriverDownloader;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeDriverService;
import org.openqa.selenium.chrome.ChromeOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * A simplified factory for creating and configuring Selenium ChromeDriver
 * instances.
 * Orchestrates calls to utility helpers for platform detection and driver
 * downloading.
 */
@Service
public class WebDriverFactory {

    private static final Logger logger = LoggerFactory.getLogger(WebDriverFactory.class);

    private final SeleniumProperties properties;
    private final WebDriverDownloader webDriverDownloader;

    public WebDriverFactory(SeleniumProperties properties, WebDriverDownloader webDriverDownloader) {
        this.properties = properties;
        this.webDriverDownloader = webDriverDownloader;
    }

    public WebDriver createDriver() {
        return createDriver(properties.isHeadless());
    }

    public WebDriver createDriver(boolean headless) {
        logger.info("🚀 Creating Chrome WebDriver...");
        logger.info("📋 Platform: {}", PlatformDetector.getPlatformInfo());

        try {
            setupChromeDriver();
            ChromeOptions options = createChromeOptions(headless);
            ChromeDriverService service = createChromeDriverService();
            WebDriver driver = new ChromeDriver(service, options);
            configureDriverTimeouts(driver);

            logger.info("✅ Chrome WebDriver created successfully (headless: {})", headless);
            return driver;
        } catch (Exception e) {
            logger.error("❌ Failed to create Chrome WebDriver: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create Chrome WebDriver", e);
        }
    }

    private void setupChromeDriver() {
        // Check if ChromeDriver system property is already set
        if (properties.getDriverManagement().isUseSystemProperty()
                && System.getProperty("webdriver.chrome.driver") != null) {
            logger.info("🔧 Using existing ChromeDriver from system property: {}",
                    System.getProperty("webdriver.chrome.driver"));
            return;
        }

        // Check for system ChromeDriver (Docker environment)
        String systemChromeDriver = findSystemChromeDriver();
        if (systemChromeDriver != null) {
            System.setProperty("webdriver.chrome.driver", systemChromeDriver);
            logger.info("🔧 Using system ChromeDriver: {}", systemChromeDriver);
            return;
        }

        if (properties.getDriverManagement().isAutoDownload()) {
            String chromeVersion = PlatformDetector.detectChromeVersion();
            String driverPath = webDriverDownloader.getDriverPath(chromeVersion);
            System.setProperty("webdriver.chrome.driver", driverPath);
            logger.info("🔧 ChromeDriver path set automatically: {}", driverPath);
        } else {
            logger.info("🔧 Automatic ChromeDriver download disabled, relying on system configuration.");
        }
    }

    private String findSystemChromeDriver() {
        // Common ChromeDriver locations in Docker/Linux environments
        String[] possiblePaths = {
                "/usr/bin/chromedriver",
                "/usr/local/bin/chromedriver",
                System.getenv("CHROMEDRIVER_PATH")
        };

        for (String path : possiblePaths) {
            if (path != null && java.nio.file.Files.exists(java.nio.file.Paths.get(path))) {
                return path;
            }
        }
        return null;
    }

    private ChromeOptions createChromeOptions(boolean headless) {
        ChromeOptions options = new ChromeOptions();

        // Set Chrome binary path if available (Docker environment)
        String chromeBinary = findChromeBinary();
        if (chromeBinary != null) {
            options.setBinary(chromeBinary);
            logger.info("🔧 Using Chrome binary: {}", chromeBinary);
        }

        if (headless) {
            options.addArguments("--headless=new");
        }

        // Docker-optimized Chrome arguments
        options.addArguments(
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection");

        options.addArguments("--user-agent=" + properties.getUserAgent());

        Map<String, Object> prefs = new HashMap<>();
        prefs.put("profile.managed_default_content_settings.images",
                properties.getPerformance().isEnableImages() ? 1 : 2);
        options.setExperimentalOption("prefs", prefs);

        return options;
    }

    private String findChromeBinary() {
        // Check environment variable first
        String chromeBin = System.getenv("CHROME_BIN");
        if (chromeBin != null && java.nio.file.Files.exists(java.nio.file.Paths.get(chromeBin))) {
            return chromeBin;
        }

        // Common Chrome binary locations in Docker/Linux environments
        String[] possiblePaths = {
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable"
        };

        for (String path : possiblePaths) {
            if (java.nio.file.Files.exists(java.nio.file.Paths.get(path))) {
                return path;
            }
        }
        return null;
    }

    private ChromeDriverService createChromeDriverService() {
        return new ChromeDriverService.Builder()
                .withSilent(true)
                .withLogLevel(org.openqa.selenium.chromium.ChromiumDriverLogLevel.WARNING)
                .usingAnyFreePort()
                .build();
    }

    private void configureDriverTimeouts(WebDriver driver) {
        driver.manage().timeouts()
                .pageLoadTimeout(properties.getPageLoadTimeout())
                .implicitlyWait(properties.getImplicitWaitTimeout());
    }

    public static boolean isDriverResponsive(WebDriver driver) {
        try {
            driver.getCurrentUrl();
            return true;
        } catch (Exception e) {
            logger.warn("WebDriver not responsive: {}", e.getMessage());
            return false;
        }
    }

    public static void quitDriver(WebDriver driver) {
        if (driver != null) {
            try {
                logger.info("🔄 Shutting down WebDriver...");
                driver.quit();
                logger.info("✅ WebDriver shut down successfully");
            } catch (Exception e) {
                logger.warn("⚠️ Error during WebDriver shutdown: {}", e.getMessage());
            }
        }
    }
}