package com.alpine.marketing.seleniumscraper.service;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.openqa.selenium.WebDriver;

/**
 * Service for processing documents (PDFs, Word documents) to extract email
 * addresses.
 * Handles PDF, DOC, and DOCX document formats only.
 */
@Service
public class DocumentProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessingService.class);

    // Email regex pattern for document content
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b");

    // Maximum document size to process (10MB)
    private static final long MAX_DOCUMENT_SIZE = 10 * 1024 * 1024;

    // Connection timeout for document download
    private static final int CONNECTION_TIMEOUT = 30000; // 30 seconds
    private static final int READ_TIMEOUT = 60000; // 60 seconds

    /**
     * Extract emails from a given URL, automatically detecting if it's a webpage or
     * a document.
     * This is the main entry point for the service.
     *
     * @param driver The WebDriver instance to use for webpages
     * @param url    The URL to process
     * @return A set of emails found
     */
    public Set<String> extractEmailsFromUrl(WebDriver driver, String url) {
        if (isDocumentUrl(url)) {
            return extractEmailsFromDocument(url);
        } else {
            return extractEmailsFromWebpage(driver, url);
        }
    }

    /**
     * Extract emails from a document URL.
     *
     * @param documentUrl The URL of the document to process
     * @return Set of email addresses found in the document
     */
    public Set<String> extractEmailsFromDocument(String documentUrl) {
        Set<String> emails = new HashSet<>();

        try {
            logger.info("📄 Processing document: {}", documentUrl);

            // Download document content
            byte[] documentContent = downloadDocument(documentUrl);
            if (documentContent == null || documentContent.length == 0) {
                logger.warn("📄 No content downloaded from: {}", documentUrl);
                return emails;
            }

            // Determine document type and extract text
            String documentText = extractTextFromDocument(documentContent, getFileExtension(documentUrl));
            if (documentText == null || documentText.trim().isEmpty()) {
                logger.info("📄 No text extracted from: {}", documentUrl);
                return emails;
            }

            // Extract emails from text content
            emails = extractEmailsFromText(documentText);
            logger.info("📄 Extracted {} emails from document: {}", emails.size(), documentUrl);

        } catch (Exception e) {
            logger.warn("📄 Failed to process document {}: {}", documentUrl, e.getMessage());
        }

        return emails;
    }

    /**
     * Extracts emails from a webpage's content using the provided WebDriver.
     *
     * @param driver The WebDriver instance
     * @param url    The URL of the webpage
     * @return A set of emails found
     */
    public Set<String> extractEmailsFromWebpage(WebDriver driver, String url) {
        try {
            driver.get(url);
            String pageSource = driver.getPageSource();
            return extractEmailsFromText(pageSource);
        } catch (Exception e) {
            logger.warn("🌐 Failed to scrape webpage {}: {}", url, e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * Download document content from URL.
     */
    private byte[] downloadDocument(String documentUrl) throws IOException {
        try {
            URL url = new URL(documentUrl);
            URLConnection connection = url.openConnection();

            // Set timeouts
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);

            // Set user agent to avoid blocking
            connection.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // Check content length
            long contentLength = connection.getContentLengthLong();
            if (contentLength > MAX_DOCUMENT_SIZE) {
                logger.warn("📄 Document too large ({} bytes): {}", contentLength, documentUrl);
                return null;
            }

            // Download content
            try (InputStream inputStream = connection.getInputStream()) {
                return inputStream.readAllBytes();
            }

        } catch (Exception e) {
            logger.warn("📄 Failed to download document {}: {}", documentUrl, e.getMessage());
            return null;
        }
    }

    /**
     * Extract text from document based on file type.
     * Supports PDF, DOC, and DOCX formats only.
     */
    private String extractTextFromDocument(byte[] documentContent, String fileExtension) {
        try {
            switch (fileExtension.toLowerCase()) {
                case "pdf":
                    return extractTextFromPdf(documentContent);
                case "doc":
                    return extractTextFromDoc(documentContent);
                case "docx":
                    return extractTextFromDocx(documentContent);
                default:
                    logger.info("📄 Unsupported document type: {} (only PDF, DOC, DOCX are supported)", fileExtension);
                    return null;
            }
        } catch (Exception e) {
            logger.warn("📄 Failed to extract text from {} document: {}", fileExtension, e.getMessage());
            return null;
        }
    }

    /**
     * Extract text from PDF document.
     */
    private String extractTextFromPdf(byte[] content) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
                PDDocument document = PDDocument.load(inputStream)) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    /**
     * Extract text from DOC document.
     */
    private String extractTextFromDoc(byte[] content) throws IOException {
        try (HWPFDocument document = new HWPFDocument(new ByteArrayInputStream(content));
                WordExtractor extractor = new WordExtractor(document)) {
            return extractor.getText();
        }
    }

    /**
     * Extract text from DOCX document.
     */
    private String extractTextFromDocx(byte[] content) throws IOException {
        try (XWPFDocument document = new XWPFDocument(new ByteArrayInputStream(content));
                XWPFWordExtractor extractor = new XWPFWordExtractor(document)) {
            return extractor.getText();
        }
    }

    /**
     * Extract emails from text content using regex.
     */
    private Set<String> extractEmailsFromText(String text) {
        Set<String> emails = new HashSet<>();

        if (text == null || text.trim().isEmpty()) {
            return emails;
        }

        Matcher matcher = EMAIL_PATTERN.matcher(text);
        while (matcher.find()) {
            String email = matcher.group().toLowerCase().trim();
            if (isBasicValidEmail(email)) {
                emails.add(email);
            }
        }

        return emails;
    }

    /**
     * Basic email validation for initial filtering.
     */
    private boolean isBasicValidEmail(String email) {
        return email != null
                && email.contains("@")
                && email.contains(".")
                && !email.startsWith("@")
                && !email.endsWith("@")
                && email.indexOf("@") == email.lastIndexOf("@")
                && email.length() >= 5;
    }

    /**
     * Get file extension from URL.
     */
    private String getFileExtension(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // Remove query parameters
        String cleanUrl = url.split("\\?")[0];

        int lastDotIndex = cleanUrl.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < cleanUrl.length() - 1) {
            return cleanUrl.substring(lastDotIndex + 1);
        }

        return "";
    }

    /**
     * Checks if a URL points to a supported document type.
     */
    public boolean isDocumentUrl(String url) {
        String extension = getFileExtension(url).toLowerCase();
        return extension.matches("pdf|doc|docx");
    }

    /**
     * Checks if a URL is likely a webpage.
     */
    public boolean isWebpageUrl(String url) {
        return !isDocumentUrl(url);
    }
}