package com.alpine.marketing.integration;

import com.alpine.marketing.seleniumscraper.service.WebDriverFactory;
import com.alpine.marketing.seleniumscraper.util.PlatformDetector;
import com.alpine.marketing.searchtask.service.GoogleApiService;
import com.alpine.marketing.searchtask.config.SearchTaskProperties;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.openqa.selenium.WebDriver;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to validate Docker environment fixes for:
 * 1. Chrome WebDriver ARM64 support
 * 2. Google API configuration loading
 */
@SpringBootTest
@ActiveProfiles("docker")
@EnabledIfEnvironmentVariable(named = "DOCKER_ENV", matches = "true")
public class DockerEnvironmentIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(DockerEnvironmentIntegrationTest.class);

    @Autowired(required = false)
    private WebDriverFactory webDriverFactory;

    @Autowired(required = false)
    private GoogleApiService googleApiService;

    @Autowired
    private SearchTaskProperties searchTaskProperties;

    @Test
    public void testPlatformDetection() {
        logger.info("Testing platform detection...");
        
        PlatformDetector.Architecture arch = PlatformDetector.detectArchitecture();
        String platformInfo = PlatformDetector.getPlatformInfo();
        
        logger.info("Detected architecture: {}", arch);
        logger.info("Platform info: {}", platformInfo);
        
        // Should not be UNSUPPORTED in Docker environment
        assertNotEquals(PlatformDetector.Architecture.UNSUPPORTED, arch, 
            "Platform should be supported in Docker environment");
        
        // Should support ARM64 Linux now
        assertTrue(arch.isSupported(), "Architecture should be supported");
    }

    @Test
    public void testChromeVersionDetection() {
        logger.info("Testing Chrome version detection...");
        
        String chromeVersion = PlatformDetector.detectChromeVersion();
        logger.info("Detected Chrome version: {}", chromeVersion);
        
        // In Docker environment, we should be able to detect Chrome/Chromium
        assertNotNull(chromeVersion, "Should be able to detect Chrome/Chromium version in Docker");
        assertFalse(chromeVersion.trim().isEmpty(), "Chrome version should not be empty");
    }

    @Test
    public void testWebDriverFactoryInitialization() {
        logger.info("Testing WebDriver factory initialization...");
        
        if (webDriverFactory == null) {
            logger.warn("WebDriverFactory is null - Selenium may be disabled");
            return;
        }
        
        // Test that we can create a WebDriver without throwing exceptions
        assertDoesNotThrow(() -> {
            WebDriver driver = null;
            try {
                driver = webDriverFactory.createDriver(true); // headless mode
                assertNotNull(driver, "WebDriver should be created successfully");
                logger.info("✅ WebDriver created successfully");
                
                // Test basic functionality
                String currentUrl = driver.getCurrentUrl();
                logger.info("WebDriver current URL: {}", currentUrl);
                
            } finally {
                if (driver != null) {
                    driver.quit();
                    logger.info("WebDriver closed successfully");
                }
            }
        }, "WebDriver creation should not throw exceptions");
    }

    @Test
    public void testGoogleApiServiceConfiguration() {
        logger.info("Testing Google API service configuration...");
        
        if (googleApiService == null) {
            logger.warn("GoogleApiService is null - may be disabled");
            return;
        }
        
        // Test configuration loading
        boolean isConfigured = googleApiService.isConfigured();
        String configSummary = googleApiService.getConfigSummary();
        
        logger.info("Google API configured: {}", isConfigured);
        logger.info("Configuration summary: {}", configSummary);
        
        // In Docker environment with proper env vars, this should be configured
        if (System.getenv("GCP_API_KEY_1") != null) {
            assertTrue(isConfigured, "Google API should be configured when environment variables are set");
        } else {
            logger.warn("GCP_API_KEY_1 environment variable not set - skipping configuration validation");
        }
    }

    @Test
    public void testSearchTaskPropertiesLoading() {
        logger.info("Testing SearchTask properties loading...");
        
        assertNotNull(searchTaskProperties, "SearchTaskProperties should be loaded");
        
        logger.info("API Keys count: {}", searchTaskProperties.getApiKeys().size());
        logger.info("CSE ID: {}", searchTaskProperties.getCseId());
        logger.info("Requests per second: {}", searchTaskProperties.getRequestsPerSecond());
        
        // Test properties summary
        String summary = searchTaskProperties.getSummary();
        logger.info("Properties summary: {}", summary);
        
        // Basic validation
        assertNotNull(searchTaskProperties.getApiKeys(), "API keys list should not be null");
        assertTrue(searchTaskProperties.getRequestsPerSecond() > 0, "Requests per second should be positive");
    }

    @Test
    public void testEnvironmentVariableResolution() {
        logger.info("Testing environment variable resolution...");
        
        // Log environment variables for debugging
        String[] envVars = {"GCP_API_KEY_1", "GCP_CSE_ID", "DB_HOST", "DB_USERNAME"};
        
        for (String envVar : envVars) {
            String value = System.getenv(envVar);
            if (value != null) {
                logger.info("Environment variable {}: {} (length: {})", 
                    envVar, value.substring(0, Math.min(4, value.length())) + "...", value.length());
            } else {
                logger.warn("Environment variable {} is not set", envVar);
            }
        }
    }
}
