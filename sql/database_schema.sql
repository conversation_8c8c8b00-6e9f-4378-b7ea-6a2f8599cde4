CREATE DATABASE IF NOT EXISTS com_alpine_school CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;

USE com_alpine_school;

CREATE TABLE search_query (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique identifier',
    site VARCHAR(255) COMMENT 'The site/domain to search within (e.g., school.edu, linkedin.com/in)',
    param VARCHAR(255) COMMENT 'The search parameters (e.g., intext:"@school.edu" -filetype:pdf)',
    required_no_of_results INT DEFAULT 100 COMMENT 'Number of results required',
    priority INT DEFAULT 5 COMMENT 'which task to do first',
    tag VARCHAR(50) DEFAULT 'blue' COMMENT 'Which process to target by the batch processing server',
    status VARCHAR(50) COMMENT 'Task status: PENDING, COMPLETED, QUOTA_FAILED, ERROR',
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When search state was first created',
    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp')
ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE search_task (
     id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique identifier',
     search_query_id INT DEFAULT 0 COMMENT 'search query',
     search_site VARCHAR(255) COMMENT 'The site/domain to search within (e.g., school.edu, linkedin.com/in)',
     search_params VARCHAR(255) COMMENT 'The search parameters (e.g., intext:"@school.edu" -filetype:pdf)',
     final_query VARCHAR(255) COMMENT 'The fully constructed query for auditing (site:domain + params)',
     urn INT DEFAULT 0 COMMENT 'School URN',
     status VARCHAR(50) DEFAULT 'PENDING' COMMENT 'Task status: PENDING, COMPLETED, QUOTA_FAILED, ERROR',
     total_results BIGINT NULL DEFAULT NULL COMMENT 'Number of URLs found by this specific task',
     error_message TEXT COMMENT 'Error details if task failed',
     created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Task creation timestamp',
     updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
     FOREIGN KEY (search_query_id) REFERENCES search_query(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'Individual search tasks with separated site and parameters - core of task-based architecture';

CREATE TABLE search_result (
   id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique identifier',
   search_task_id INT NOT NULL COMMENT 'The search task that discovered this URL',
   url TEXT NOT NULL COMMENT 'The discovered URL (can be very long)',
   urn INT DEFAULT 0 COMMENT 'School URN',
   title VARCHAR(512) NULL COMMENT 'Page title returned from Google Search result',
   snippet TEXT NULL COMMENT 'Short description or snippet of the page returned by Google Search',
   created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When URL was found and recorded',
   updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last modification timestamp',
   scanned TIMESTAMP NULL COMMENT 'When this URL was processed by Selenium scraper (NULL = unscanned)',
   FOREIGN KEY (search_task_id) REFERENCES search_task(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'URLs discovered by Google search tasks, ready for Selenium email extraction';

CREATE TABLE IF NOT EXISTS school (
    urn INT PRIMARY KEY COMMENT 'Unique identifier)',
    school VARCHAR(500) COMMENT 'Full school name',
    website VARCHAR(255) COMMENT 'Primary school website URL',
    email_domain VARCHAR(255) COMMENT 'Expected email domain for the school (e.g., school.edu)',
    web_domain VARCHAR(255) COMMENT 'Web domain to search within (e.g., school.edu)',
    email_pattern VARCHAR(100) NULL COMMENT 'The most common email pattern inferred from found contacts (e.g., {first}.{last}, {f}{last})',
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last modification timestamp'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'Master table of educational institutions with domain information for targeted email discovery';

CREATE TABLE IF NOT EXISTS school_scraped_email (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Unique email record identifier',
    urn INT NOT NULL COMMENT 'School URN',
    email VARCHAR(320) NOT NULL COMMENT 'Full email address (RFC 5321 maximum length)',
    source TEXT COMMENT 'Source URL where email was found (can be very long)',
    search_result_id INT NOT NULL,
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last modification timestamp',
    FOREIGN KEY (search_result_id) REFERENCES search_result(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'Stage 1: Raw emails from Selenium scraping before validation and cleaning';

CREATE TABLE IF NOT EXISTS `linkedin_profile` (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique identifier',
    search_result_id INT NOT NULL,
    `first_name` varchar(255) DEFAULT NULL COMMENT 'Contact first name',
    `last_name` varchar(255) DEFAULT NULL COMMENT 'Contact last name',
    `role` varchar(255) DEFAULT NULL COMMENT 'Job title or role at the institution',
    `linkedin_url` varchar(512) NOT NULL COMMENT 'LinkedIn profile URL',
    `school_urn` varchar(20) DEFAULT NULL COMMENT 'Associated school URN',
    `email` VARCHAR(320) NULL COMMENT 'Email address found by Hunter.io or inferred from patterns',
    `method` VARCHAR(50) NULL COMMENT 'The method used to find the email (e.g., hunter_finder, inferred_pattern)',
    `hunter_processed_at` TIMESTAMP NULL COMMENT 'Timestamp when this profile was processed by Hunter.io',
    `found_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Discovery timestamp',
FOREIGN KEY (search_result_id) REFERENCES search_result(id) ON DELETE CASCADE

) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'LinkedIn profile with Hunter.io email discovery and pattern inference support';