insert into search_query (id, site, param, required_no_of_results, priority, tag, status, created, updated)
values  (1, '{{school_web_domain}}', '(intext:"@{{school_email_domain}}") -filetype:pdf -filetype:doc -filetype:docx', 100, 1, 'blue', 'PENDING', '2025-07-14 16:24:25', '2025-07-15 00:48:25'),
        (2, '{{school_web_domain}}', '(filetype:pdf OR filetype:doc OR filetype:docx) (intext:" @{{school_email_domain}}")', 100, 2, 'blue', 'PENDING', '2025-07-14 16:24:25', '2025-07-15 00:48:25'),
        (3, 'linkedin.com/in', 'intext:"{{school_web_domain}}" (intext:"QTS" OR intext:"PGCE")', 100, 3, 'blue', 'PENDING', '2025-07-14 16:24:25', '2025-07-14 17:13:16'),
        (4, 'nintendo.com', 'mario', 100, 4, 'blue', 'PENDING', '2025-07-14 20:09:00', '2025-07-14 20:09:00');