USE com_alpine_school;
-- Add email verification columns to linkedin_profile table
-- Based on Hunter.io Email Verifier API response structure
ALTER TABLE linkedin_profile
ADD COLUMN hunter_verify_at TIMESTAMP NULL COMMENT 'Timestamp when email was verified by Hunter.io',
    ADD COLUMN status VARCHAR(20) NULL COMMENT 'Email verification status: valid, invalid, accept_all, webmail, disposable, unknown',
    ADD COLUMN result VARCHAR(20) NULL COMMENT 'Main verification result: deliverable, undeliverable, risky (deprecated)',
    ADD COLUMN score INT NULL COMMENT 'Deliverability score (0-100) provided by Hunter.io',
    ADD COLUMN accept_all BOOLEAN NULL COMMENT 'TRUE if SMTP server accepts all email addresses',
    ADD COLUMN block BOOLEAN NULL COMMENT 'TRUE if SMTP server prevented verification check';
-- Add index on verification status for efficient filtering
CREATE INDEX idx_linkedin_profile_verification_status ON linkedin_profile(status);
-- Add index on verification timestamp for efficient querying
CREATE INDEX idx_linkedin_profile_hunter_verify_at ON linkedin_profile(hunter_verify_at);
-- Add composite index for filtering inferred patterns ready for verification
CREATE INDEX idx_linkedin_profile_method_status ON linkedin_profile(method, status);