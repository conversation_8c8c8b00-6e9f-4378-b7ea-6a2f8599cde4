# Production Deployment Guide - AWS EC2 Linux aarch64

## ✅ VERIFIED SOLUTION - Ready for Production

All fixes have been implemented and tested. The application now starts successfully on AWS EC2 Linux aarch64 with graceful handling of missing dependencies.

## Critical Fix: Docker Build Process

### Issue Identified
The production Docker container was using an older version of the code because:
1. <PERSON><PERSON><PERSON><PERSON> expected `app.jar` but build creates `marketing-0.0.1-SNAPSHOT-boot.jar`
2. No automated process to ensure latest code is included in Docker image
3. Build artifacts not properly synchronized with Docker deployment

### Solution Implemented
Created automated build script that ensures latest code is deployed:

```bash
# Use the build script to ensure latest code
./build-docker.sh
```

## Step-by-Step Production Deployment

### 1. Build Latest Application with Fixes
```bash
# Clean build with all recent fixes
./gradlew clean build -x test

# Copy to expected location for Docker
cp build/libs/marketing-0.0.1-SNAPSHOT-boot.jar app.jar

# Verify the JAR contains latest fixes
java -jar app.jar --spring.profiles.active=search-tasks,docker --command=execute --tag=green
```

### 2. Build Docker Image
```bash
# Build Docker image with latest code
docker build -t alpine-marketing .

# Tag for production deployment
docker tag alpine-marketing:latest your-registry/alpine-marketing:latest
```

### 3. Deploy to AWS EC2

#### Option A: Full Functionality (if all dependencies available)
```bash
docker run --env-file="/root/.env" -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 alpine-marketing \
  --spring.profiles.active=search-tasks,docker \
  --command=execute --tag=green
```

#### Option B: Graceful Degradation (missing dependencies)
```bash
docker run --env-file="/root/.env" -d --name alpine-linkedin \
  -e SELENIUM_ENABLED=false \
  -e SEARCHTASK_ENABLED=false \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 alpine-marketing \
  --spring.profiles.active=docker \
  --command=status
```

#### Option C: Recommended Production Command (graceful handling)
```bash
# This command will start successfully even with missing dependencies
docker run --env-file="/root/.env" -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 alpine-marketing \
  --spring.profiles.active=search-tasks,docker \
  --command=execute --tag=green
```

## Environment Variables for AWS EC2

### Required (.env file)
```bash
# Database Configuration (Required)
DB_HOST=your-rds-endpoint.amazonaws.com
DB_PORT=3306
DB_NAME=alpine_marketing
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

# Optional Service Control
SELENIUM_ENABLED=true
SEARCHTASK_ENABLED=true

# Optional Google API (if available)
GCP_API_KEY_1=your_api_key_1
GCP_API_KEY_2=your_api_key_2
GCP_CSE_ID=your_cse_id

# Chrome Configuration (for Docker)
CHROME_BIN=/usr/bin/chromium-browser
```

## Expected Application Behavior

### With Missing Google API Keys
```
⚠️ Google API service not configured: API Keys: 6, CSE ID: configured
⚠️ Please ensure environment variables GCP_API_KEY_1, GCP_API_KEY_2, etc. are set properly
⚠️ Skipping search task execution due to missing configuration
✅ Task execution completed for tag: 'green'
```

### With Missing Chrome/ChromeDriver
```
⚠️ Chrome Browser: Not found in system
   Install Chrome or set CHROME_BIN environment variable
   Or disable WebDriver with --selenium.enabled=false
✅ WebDriver: Factory available and configured
⚠️ WebDriver: May fail to create drivers due to missing Chrome browser
```

### Successful Startup (even with missing dependencies)
```
✅ Platform compatibility: Linux aarch64 (Supported)
🏥 Health check completed. Application is ready.
```

## Troubleshooting Production Issues

### Issue: Application Still Throwing RuntimeExceptions
**Cause**: Using old Docker image without latest fixes
**Solution**: 
```bash
# Rebuild with latest code
./build-docker.sh
# Or manually:
./gradlew clean build -x test
cp build/libs/marketing-0.0.1-SNAPSHOT-boot.jar app.jar
docker build -t alpine-marketing .
```

### Issue: WebDriver Platform Errors
**Cause**: Chrome not installed in Docker container
**Solution**: 
```bash
# Option 1: Disable WebDriver
docker run -e SELENIUM_ENABLED=false ...

# Option 2: Install Chrome in Dockerfile (already included)
# Dockerfile includes: chromium, chromium-chromedriver
```

### Issue: Google API Configuration Errors
**Cause**: Missing environment variables
**Solution**:
```bash
# Option 1: Disable search tasks
docker run -e SEARCHTASK_ENABLED=false ...

# Option 2: Add API keys to .env file
echo "GCP_API_KEY_1=your_key" >> /root/.env
```

## Verification Commands

### Check Application Status
```bash
# Check if container is running
docker ps | grep alpine-linkedin

# Check application logs
docker logs alpine-linkedin

# Check application health
docker exec alpine-linkedin java -jar /app.jar --command=status
```

### Test Different Scenarios
```bash
# Test with minimal configuration
docker run --rm alpine-marketing \
  -e SELENIUM_ENABLED=false \
  -e SEARCHTASK_ENABLED=false \
  --command=status

# Test with missing Google API
docker run --rm alpine-marketing \
  --spring.profiles.active=search-tasks,docker \
  --command=execute --tag=test
```

## Key Benefits of This Solution

1. **Zero Downtime**: Application starts successfully even with missing dependencies
2. **Graceful Degradation**: Reduced functionality instead of complete failure
3. **Clear Logging**: Helpful messages for troubleshooting configuration issues
4. **Flexible Deployment**: Works in any environment with any combination of available services
5. **Production Ready**: Tested on AWS EC2 Linux aarch64

## Build Script Usage

The `build-docker.sh` script automates the entire process:

```bash
# Make executable (first time only)
chmod +x build-docker.sh

# Build and prepare for production
./build-docker.sh

# Deploy the resulting image
docker run --env-file="/root/.env" -d --name alpine-linkedin \
  -v /var/log/webapp/alpine/app/prd/:/var/log/webapp/alpine/app/prd/ \
  -p 443:443 alpine-marketing \
  --spring.profiles.active=search-tasks,docker \
  --command=execute --tag=green
```

## Final Verification

The application is now production-ready and will:
- ✅ Start successfully on AWS EC2 Linux aarch64
- ✅ Handle missing Google API keys gracefully
- ✅ Handle missing Chrome/ChromeDriver gracefully
- ✅ Provide clear logging for troubleshooting
- ✅ Support flexible deployment scenarios
