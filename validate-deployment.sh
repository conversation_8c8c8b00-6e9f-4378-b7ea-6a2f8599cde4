#!/bin/bash

# Deployment validation script for AWS EC2 Amazon Linux 2023
# This script validates the fixes work in the actual deployment environment

set -e

echo "🚀 Validating Spring Boot Application Deployment"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if running on Amazon Linux 2023
print_status "INFO" "Checking system information..."
if [ -f /etc/os-release ]; then
    source /etc/os-release
    print_status "INFO" "OS: $NAME $VERSION_ID"
    print_status "INFO" "Architecture: $(uname -m)"
    
    if [[ "$NAME" == *"Amazon Linux"* ]]; then
        print_status "SUCCESS" "Running on Amazon Linux"
    else
        print_status "WARNING" "Not running on Amazon Linux - results may vary"
    fi
else
    print_status "WARNING" "Cannot determine OS version"
fi

# Check Docker installation
print_status "INFO" "Checking Docker installation..."
if command -v docker &> /dev/null; then
    docker_version=$(docker --version)
    print_status "SUCCESS" "Docker installed: $docker_version"
else
    print_status "ERROR" "Docker is not installed"
    exit 1
fi

# Check if .env file exists
print_status "INFO" "Checking environment configuration..."
if [ -f "/root/.env" ]; then
    print_status "SUCCESS" "Environment file found at /root/.env"
    
    # Check for required environment variables
    required_vars=("GCP_API_KEY_1" "GCP_CSE_ID" "DB_HOST" "DB_USERNAME" "DB_PASSWORD")
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" /root/.env; then
            print_status "SUCCESS" "Environment variable $var is set"
        else
            print_status "WARNING" "Environment variable $var is missing"
        fi
    done
else
    print_status "ERROR" "Environment file not found at /root/.env"
    print_status "INFO" "Please create /root/.env with required variables:"
    echo "GCP_API_KEY_1=your_api_key"
    echo "GCP_CSE_ID=your_cse_id"
    echo "DB_HOST=your_db_host"
    echo "DB_USERNAME=your_db_user"
    echo "DB_PASSWORD=your_db_password"
    exit 1
fi

# Check if Docker image exists
print_status "INFO" "Checking Docker image..."
if docker images | grep -q "alpine"; then
    print_status "SUCCESS" "Alpine Docker image found"
else
    print_status "WARNING" "Alpine Docker image not found - may need to build"
fi

# Test Chrome/ChromeDriver in container
print_status "INFO" "Testing Chrome/ChromeDriver installation in container..."
if docker run --rm alpine chromium-browser --version > /dev/null 2>&1; then
    print_status "SUCCESS" "Chromium is working in container"
else
    print_status "ERROR" "Chromium test failed in container"
fi

if docker run --rm alpine chromedriver --version > /dev/null 2>&1; then
    print_status "SUCCESS" "ChromeDriver is working in container"
else
    print_status "ERROR" "ChromeDriver test failed in container"
fi

# Test application startup (if image exists)
if docker images | grep -q "alpine"; then
    print_status "INFO" "Testing application startup (30 second timeout)..."
    
    # Run application with timeout and capture logs
    timeout 30s docker run --env-file="/root/.env" --rm alpine \
        --spring.profiles.active=docker 2>&1 | tee deployment-test.log || true
    
    print_status "INFO" "Analyzing application logs..."
    
    # Check for critical success indicators
    if grep -q "Started.*Application" deployment-test.log; then
        print_status "SUCCESS" "Application started successfully"
    elif grep -q "SearchTask Configuration Loaded" deployment-test.log; then
        print_status "SUCCESS" "Application configuration loaded successfully"
    else
        print_status "WARNING" "Application startup may have issues"
    fi
    
    # Check for specific fixes
    if grep -q "Docker environment detected: true" deployment-test.log; then
        print_status "SUCCESS" "Docker environment detection working"
    fi
    
    if grep -q "Using system ChromeDriver" deployment-test.log; then
        print_status "SUCCESS" "System ChromeDriver being used"
    fi
    
    if grep -q "API Keys: [1-9]" deployment-test.log; then
        print_status "SUCCESS" "API keys loaded successfully"
    fi
    
    if grep -q "Unsupported platform" deployment-test.log; then
        print_status "ERROR" "Platform support issue detected"
    else
        print_status "SUCCESS" "No platform support errors"
    fi
    
    # Cleanup
    rm -f deployment-test.log
else
    print_status "WARNING" "Skipping application test - Docker image not found"
fi

print_status "INFO" "Deployment validation completed!"
print_status "INFO" "If all checks passed, the application should work correctly."
