---
alwaysApply: true
description: Core Java Spring Boot development standards with JDBC
---

# Java Spring Boot + Pure JDBC Development Rules

## CRITICAL SQL REQUIREMENTS - STRICTLY ENFORCED
- **FORBIDDEN**: N+1 query problems - You MUST NOT create multiple queries when one JOIN would suffice
- **REQUIRED**: Write raw SQL that follows real-world database practices
- **MANDATORY**: Use efficient JOIN queries instead of multiple separate queries
- **ESSENTIAL**: Implement proper pagination and filtering at database level using LIMIT/OFFSET
- **REQUIRED**: Use batch operations for multiple inserts/updates when applicable
- **MANDATORY**: Optimize SQL queries for performance and avoid unnecessary data retrieval

## Architecture Constraints
- Java Spring Boot + Pure JDBC Template only
- Spring Data Repository pattern WITHOUT JPA/Hibernate
- NO lazy loading, NO annotations-based ORM
- NO Concurrency patterns or Spring Batch
- Direct SQL queries with proper parameter binding
- Simple database connection management

## Database Design
- **CRITICAL**: Identify OLTP vs OLAP nature first - affects all design decisions
- **REQUIRED**: Follow 3NF for OLTP, denormalize for OLAP/reporting
- **DATA INTEGRITY**: Eliminate duplicate inconsistent data (biggest enemy)
- **NAMING**: Use snake_case for tables/columns (user_id, created_at)
- **AVOID**: Comma-separated values in columns, use separate tables instead
- **PERFORMANCE**: Denormalize when queries are more important than updates
- **MIGRATIONS**: Version all schema changes, test on real data size
- **BLUE-GREEN READY**: New columns nullable first, remove old columns later

## Code Quality Standards
- Maximum 200-300 lines per file
- Eliminate redundant variables/expressions
- NO code duplication
- Self-explanatory naming conventions
- Follow principle: "do the simple things first"
- Minimal comments - code should be self-documenting
- Proper error handling without over-engineering

## SQL Best Practices
- Use meaningful table aliases (u for users, p for products, etc.)
- Always use parameterized queries to prevent SQL injection
- Implement proper indexing considerations in queries
- Use EXISTS instead of IN for better performance
- Implement proper transaction management
- Use batch operations: jdbcTemplate.batchUpdate() for multiple operations

## CONVERSATION STYLE:
- Be concise but thorough
- Always provide working code examples
- Ask clarifying questions when requirements are ambiguous
- Suggest improvements and optimizations
- Warn about potential performance issues
- Provide testing recommendations

## ERROR HANDLING:
- When encountering issues, provide specific solutions
- Explain the root cause of problems
- Offer alternative approaches
- Include proper exception handling in code examples

## CONTEXT RETENTION:
- Remember project architecture decisions
- Track database schema changes
- Maintain coding patterns consistency
- Remember performance optimization choices
- Keep track of implemented features

## PRIORITY INFORMATION:
1. Database schema and relationships
2. Implemented service patterns
3. Error handling approaches
4. Performance optimization decisions
5. Testing strategies used