---
alwaysApply: true
description: MCP Server integration rules for enhanced Java Spring Boot development
---

# MCP Integration Rules for Cursor AI (HINT):
Use JetBrains MCP for inspection, GitHub MCP for repo context, Context7 MCP for docs check, Git MCP before commit.

## ACTIVE MCP SERVERS
- **JetBrains MCP**: IDE integration and code analysis
- **GitHub MCP**: Repository management and code collaboration
- **Git MCP**: Version control operations
- **Context7 MCP**: Library documentation and dependency resolution

## JETBRAINS MCP INTEGRATION

### Code Analysis & Refactoring
- Use JetBrains MCP for identifying code smells and optimization opportunities
- Request inspection results before major refactoring
- Leverage IDE-style error detection and suggestions
- Apply JetBrains code style recommendations for Java/Spring Boot

### Integration Patterns
```java
// When requesting code generation, specify:
// - JetBrains inspection requirements
// - Code style preferences (Google Java Style, IntelliJ IDEA style)
// - Performance optimization hints from IDE analysis
```

## GITHUB MCP INTEGRATION

### Repository Context Awareness
- Always reference current branch and recent commits when generating code
- Check existing codebase patterns before suggesting new implementations
- Consider open PRs and issues when proposing solutions
- Maintain consistency with established project conventions

### Collaboration Patterns
```java
// Before code generation, reference:
// - Recent commit history for context
// - Branch naming conventions
// - PR review feedback patterns
// - Team coding standards from repo history
```

### Integration Commands
- Use GitHub MCP to check existing implementations before creating new ones
- Reference issue discussions for requirement clarification
- Consider reviewer feedback patterns when generating code
- Align with team's established patterns from commit history

## GIT MCP INTEGRATION

### Version Control Awareness
- Check git status before suggesting file modifications
- Consider uncommitted changes when generating code
- Respect .gitignore patterns for generated files
- Suggest appropriate commit message patterns

### Workflow Integration
```bash
# Before major code changes:
# 1. Check current branch status via Git MCP
# 2. Identify modified files that might conflict
# 3. Suggest appropriate branching strategy
# 4. Recommend commit granularity
```

## CONTEXT7 MCP INTEGRATION

### Library Documentation Access
- Always verify latest Spring Boot documentation before suggesting features
- Cross-reference library compatibility and versions
- Use Context7 to resolve dependency conflicts
- Validate API usage against official documentation

### Dependency Management
```xml
<!-- When adding dependencies, use Context7 to verify: -->
<!-- - Latest stable versions -->
<!-- - Security vulnerabilities -->
<!-- - Compatibility with existing dependencies -->
<!-- - Alternative library recommendations -->
```

### Documentation Patterns
- Reference official Spring documentation via Context7
- Validate JDBC patterns against official examples
- Check database driver compatibility
- Verify performance best practices from official sources

## COMBINED MCP WORKFLOW

### Pre-Code Generation Checklist
1. **Git MCP**: Check current working directory status
2. **GitHub MCP**: Review recent changes and team patterns
3. **JetBrains MCP**: Analyze existing code for improvement opportunities
4. **Context7 MCP**: Verify library documentation and best practices

### Code Generation Strategy
```java
// When generating Spring Boot code:
// 1. Use GitHub MCP to understand existing patterns
// 2. Apply JetBrains style guidelines
// 3. Verify Spring Boot patterns via Context7
// 4. Consider git workflow via Git MCP

@Service
@Transactional
public class UserService {
    // Code generated with:
    // - GitHub patterns awareness
    // - JetBrains style compliance
    // - Context7 verified Spring patterns
    // - Git-friendly structure
}
```

### Error Resolution Workflow
1. **JetBrains MCP**: Identify specific error types and inspection results
2. **Context7 MCP**: Look up official documentation for proper solutions
3. **GitHub MCP**: Check if similar issues were resolved in project history
4. **Git MCP**: Ensure solutions don't conflict with version control

## MCP-ENHANCED PROMPTING

### Context-Rich Prompts
```
// Example enhanced prompt structure:
"Using GitHub MCP, check our existing UserRepository patterns,
then apply JetBrains code style via JetBrains MCP,
verify Spring Boot JDBC patterns through Context7 MCP,
and ensure the solution is git-workflow friendly via Git MCP.

Generate a UserRepository with batch operations following our established patterns."
```

### Quality Assurance Integration
- **JetBrains MCP**: Code quality metrics and inspection results
- **GitHub MCP**: Team review patterns and approval criteria
- **Context7 MCP**: Official documentation compliance
- **Git MCP**: Change impact analysis and conflict prevention

## PERFORMANCE OPTIMIZATION WITH MCP

### Analysis Pipeline
1. **JetBrains MCP**: Performance profiling insights
2. **GitHub MCP**: Historical performance issue patterns
3. **Context7 MCP**: Spring Boot performance best practices
4. **Git MCP**: Performance regression tracking

### Implementation Verification
```java
// MCP-verified performance pattern:
@Repository
public class OptimizedUserRepository {
    // JetBrains MCP: Verified for performance anti-patterns
    // GitHub MCP: Aligned with team's optimization patterns
    // Context7 MCP: Following official Spring JDBC guidelines
    // Git MCP: Structured for clean version control

    public List<UserDto> findUsersWithBatchOptimization() {
        // Implementation verified across all MCP sources
    }
}
```

## TESTING STRATEGY WITH MCP

### Test Generation
- **JetBrains MCP**: Test coverage analysis and suggestions
- **GitHub MCP**: Team testing patterns and conventions
- **Context7 MCP**: Spring Boot testing best practices
- **Git MCP**: Test file organization and versioning

### Integration Testing
```java
// MCP-informed testing approach:
@SpringBootTest
class UserServiceIntegrationTest {
    // Pattern validated by:
    // - JetBrains testing guidelines
    // - GitHub team conventions
    // - Context7 Spring Boot testing docs
    // - Git-friendly test structure
}
```

## CONTINUOUS IMPROVEMENT

### Feedback Loop
1. Use JetBrains MCP for code quality metrics
2. Track GitHub MCP patterns for team alignment
3. Stay updated with Context7 documentation changes
4. Monitor Git MCP for workflow efficiency

### Rule Updates
- Regularly sync with latest MCP capabilities
- Update patterns based on team feedback via GitHub MCP
- Incorporate new Spring Boot features via Context7 MCP
- Optimize git workflows based on Git MCP insights

## MCP TROUBLESHOOTING

### Common Issues
- **Connection**: Verify MCP server status before complex operations
- **Rate Limits**: Distribute requests across multiple MCP servers
- **Context**: Ensure each MCP has sufficient context for accurate responses
- **Conflicts**: Resolve conflicting recommendations between MCP sources

### Resolution Strategy
1. Prioritize Context7 for official documentation conflicts
2. Use JetBrains for code quality disputes
3. Default to GitHub patterns for team consistency
4. Apply Git MCP for workflow-related decisions