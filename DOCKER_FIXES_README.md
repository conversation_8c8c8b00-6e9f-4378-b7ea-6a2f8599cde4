# Docker Environment Fixes for Spring Boot Application

This document describes the fixes implemented to resolve Chrome WebDriver and Google API configuration issues when running the Spring Boot application in Docker containers on AWS EC2 with Amazon Linux 2023.

## Issues Fixed

### 1. Chrome WebDriver Platform Support Error
**Problem**: `Unsupported platform for automatic ChromeDriver management: Platform: Linux 6.1.141-165.249.amzn2023.aarch64 (aarch64)`

**Root Cause**: The `PlatformDetector` class didn't support ARM64 Linux architecture.

**Solution**:
- Added `LINUX_ARM64` architecture support to `PlatformDetector.Architecture` enum
- Updated `detectArchitecture()` method to recognize ARM64 Linux systems
- Enhanced Chrome version detection to try multiple Chrome/Chromium binaries
- Added system ChromeDriver detection for Docker environments

### 2. Google API Configuration Missing
**Problem**: `Google API service not configured: API Keys: 0, CSE ID: missing, Rate: 0.0/sec`

**Root Cause**: Configuration not properly loaded from `application-docker.yml` or environment variables not set.

**Solution**:
- Enhanced `application-docker.yml` with proper environment variable placeholders
- Added fallback CSE ID configuration
- Improved error logging and configuration validation

## Files Modified

### Core Platform Support
- `src/main/java/com/alpine/marketing/seleniumscraper/util/PlatformDetector.java`
  - Added ARM64 Linux support
  - Enhanced Chrome binary detection
  - Improved version detection for Docker environments

### WebDriver Configuration
- `src/main/java/com/alpine/marketing/seleniumscraper/service/WebDriverFactory.java`
  - Added system ChromeDriver detection
  - Enhanced Chrome binary path resolution
  - Added Docker-optimized Chrome options

### Docker Configuration
- `Dockerfile`
  - Improved Chrome/Chromium installation for ARM64
  - Added security improvements (non-root user)
  - Enhanced environment variable setup

- `src/main/resources/application-docker.yml`
  - Added comprehensive Selenium configuration for Docker
  - Configured system ChromeDriver usage
  - Optimized performance settings for containers

## Environment Variables Required

For proper operation in Docker/AWS EC2, set these environment variables:

```bash
# Database Configuration
DB_HOST=your-rds-endpoint.amazonaws.com
DB_PORT=3306
DB_NAME=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Google API Configuration
GCP_API_KEY_1=your_api_key_1
GCP_API_KEY_2=your_api_key_2
GCP_API_KEY_3=your_api_key_3
GCP_API_KEY_4=your_api_key_4
GCP_API_KEY_5=your_api_key_5
GCP_API_KEY_6=your_api_key_6
GCP_CSE_ID=your_custom_search_engine_id

# Spring Profile
SPRING_PROFILES_ACTIVE=docker
```

## Testing the Fixes

### 1. Quick Validation
Run the provided test script:
```bash
./test-docker-fixes.sh
```

### 2. Full Integration Tests
Run with integration tests:
```bash
./test-docker-fixes.sh --run-tests
```

### 3. Manual Docker Testing
```bash
# Build the image
docker build -t alpine-marketing .

# Test Chrome installation
docker run --rm alpine-marketing chromium-browser --version
docker run --rm alpine-marketing chromedriver --version

# Run with environment variables
docker run --rm \
  --env SPRING_PROFILES_ACTIVE=docker \
  --env DB_HOST=your-host \
  --env DB_USERNAME=your-user \
  --env DB_PASSWORD=your-password \
  --env GCP_API_KEY_1=your-key \
  --env GCP_CSE_ID=your-cse-id \
  alpine-marketing
```

## Deployment to AWS EC2

### 1. Build and Push to ECR
```bash
# Build for ARM64 (if using ARM-based EC2 instances)
docker buildx build --platform linux/arm64 -t your-ecr-repo:latest .

# Push to ECR
docker push your-ecr-repo:latest
```

### 2. EC2 Instance Requirements
- Amazon Linux 2023 (ARM64 or AMD64)
- Docker installed
- Proper IAM roles for ECR access
- Security groups configured for application ports

### 3. Environment Configuration
Create a `.env` file or use AWS Systems Manager Parameter Store for environment variables.

## Verification Steps

After deployment, verify:

1. **Platform Detection**: Check logs for correct architecture detection
2. **Chrome Installation**: Verify Chrome/Chromium and ChromeDriver are found
3. **WebDriver Creation**: Ensure WebDriver instances can be created
4. **Google API**: Confirm API keys and CSE ID are loaded correctly
5. **Search Tasks**: Test search task execution with "green" tag

## Troubleshooting

### Chrome WebDriver Issues
- Check Chrome binary path: `/usr/bin/chromium-browser`
- Verify ChromeDriver path: `/usr/bin/chromedriver`
- Ensure proper permissions for non-root user

### Google API Issues
- Verify environment variables are set correctly
- Check API key validity and quotas
- Confirm CSE ID is correct

### Performance Optimization
- Adjust WebDriver pool size based on instance resources
- Monitor memory usage with multiple WebDriver instances
- Consider disabling images for better performance

## Architecture Support Matrix

| Platform | Architecture | Status | Notes |
|----------|-------------|--------|-------|
| macOS | ARM64 (M1/M2) | ✅ Supported | Original support |
| Linux | AMD64 | ✅ Supported | Original support |
| Linux | ARM64 | ✅ **NEW** | Added in this fix |
| Windows | Any | ❌ Not tested | Not targeted for Docker |

## Next Steps

1. Deploy to staging environment for validation
2. Monitor application startup and WebDriver creation
3. Test search task execution with real data
4. Set up monitoring and alerting for WebDriver failures
5. Consider implementing WebDriver health checks
